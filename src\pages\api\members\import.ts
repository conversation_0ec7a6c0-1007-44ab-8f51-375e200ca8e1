import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/services/auth/auth-options';
import { prisma } from '@/lib/prisma';
import formidable from 'formidable';
import { parse } from 'csv-parse/sync';
import { z } from 'zod';
import fs from 'fs';

// Disable body parser for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

const csvMemberSchema = z.object({
  email: z.string().email({ message: "Invalid email format" }),
  firstName: z.string().min(1, { message: "First name cannot be empty" }),
  lastName: z.string().min(1, { message: "Last name cannot be empty" }),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  // Note: address, department, gender, maritalStatus, occupation are not in the current Member schema
  // They would need to be added to the schema or handled differently
});

type CsvMemberData = z.infer<typeof csvMemberSchema>;

interface ImportResults {
  successful: number;
  failed: number;
  errors: string[];
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ImportResults | { error: string }>
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Parse the uploaded file
    const form = formidable({
      maxFileSize: 10 * 1024 * 1024, // 10MB limit
      filter: ({ mimetype }) => {
        return mimetype === 'text/csv' || mimetype === 'application/csv';
      },
    });

    const [fields, files] = await form.parse(req);
    const file = Array.isArray(files.file) ? files.file[0] : files.file;

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Read and parse CSV content
    const fileContent = fs.readFileSync(file.filepath, 'utf-8');
    
    let records;
    try {
      records = parse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
      });
    } catch (parseError) {
      return res.status(400).json({ error: 'Invalid CSV format' });
    }

    if (!Array.isArray(records) || records.length === 0) {
      return res.status(400).json({ error: 'CSV file is empty or invalid' });
    }

    // Validate required headers
    const requiredHeaders = ['email', 'firstName', 'lastName'];
    const firstRecord = records[0];
    const missingHeaders = requiredHeaders.filter(header => !(header in firstRecord));

    if (missingHeaders.length > 0) {
      return res.status(400).json({
        error: `Missing required CSV headers: ${missingHeaders.join(', ')}`
      });
    }

    const results: ImportResults = {
      successful: 0,
      failed: 0,
      errors: [],
    };

    // Process each record
    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const rowNumber = i + 2; // +2 because CSV is 1-indexed and has header row

      try {
        // Validate the record
        const validatedData = csvMemberSchema.parse(record);
        
        // Check if member already exists
        const existingMember = await prisma.member.findUnique({
          where: { email: validatedData.email },
        });

        if (existingMember) {
          results.failed++;
          results.errors.push(`Row ${rowNumber}: Member with email ${validatedData.email} already exists`);
          continue;
        }

        // Create the member
        await prisma.member.create({
          data: {
            firstName: validatedData.firstName,
            lastName: validatedData.lastName,
            email: validatedData.email,
            phone: validatedData.phone || null,
            dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : null,
            organizationId: session.user.organizationId || '',
          },
        });

        results.successful++;
      } catch (error) {
        results.failed++;
        let errorMessage = `Row ${rowNumber}: `;
        
        if (error instanceof z.ZodError) {
          const fieldErrors = error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ');
          errorMessage += fieldErrors;
        } else if (error instanceof Error) {
          errorMessage += error.message;
        } else {
          errorMessage += 'Unknown error occurred';
        }
        
        results.errors.push(errorMessage);
      }
    }

    // Clean up uploaded file
    try {
      fs.unlinkSync(file.filepath);
    } catch (cleanupError) {
      console.warn('Failed to clean up uploaded file:', cleanupError);
    }

    return res.status(200).json(results);
  } catch (error) {
    console.error('Import error:', error);
    return res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Internal server error' 
    });
  }
}
