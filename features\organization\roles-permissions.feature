@mvp @p1 @organization @security
Feature: Roles and Permissions Management
  As a Church Administrator
  I want to manage user roles and permissions
  So that I can control access to different features based on user responsibilities

  Background:
    Given I am logged in as a Church Administrator
    And my organization is set up
    And I have permissions to manage roles and users
    And the role management system is available

  @p1 @smoke
  Scenario: Default role assignment for new users
    Given the system has default roles configured:
      | Role Name        | Description                    | Default Permissions           |
      | Administrator    | Full system access             | All permissions               |
      | Pastor           | Ministry and member management | Member, Service, Report access|
      | Staff Member     | Daily operations               | Member, Attendance access     |
      | Volunteer        | Limited ministry access        | Basic member view             |
      | Member           | Personal access only           | Own profile access            |
    When a new user "<EMAIL>" is added to the system
    And no specific role is assigned
    Then the user should be assigned the "Member" role by default
    And the user should have basic member permissions
    And the role assignment should be logged

  @p1
  Scenario: Custom role creation
    Given I want to create a role for youth ministry leaders
    When I navigate to "Roles & Permissions"
    And I click "Create New Role"
    And I enter role details:
      | Field           | Value                           |
      | Role Name       | Youth Ministry Leader           |
      | Description     | Manages youth programs          |
      | Department      | Youth Ministry                  |
    And I assign permissions:
      | Permission Category | Specific Permissions              |
      | Member Management   | View youth members, Edit profiles |
      | Attendance         | Record youth attendance           |
      | Events             | Create youth events               |
      | Reports            | View youth reports                |
    And I click "Create Role"
    Then the custom role should be created successfully
    And the role should be available for user assignment
    And the permissions should be properly configured

  @p1
  Scenario: User role assignment
    Given user "<EMAIL>" exists with "Member" role
    And I want to promote her to "Youth Ministry Leader"
    When I navigate to "User Management"
    And I search for "<EMAIL>"
    And I click "Edit User"
    And I change the role from "Member" to "Youth Ministry Leader"
    And I click "Save Changes"
    Then Sarah's role should be updated successfully
    And she should gain the permissions associated with "Youth Ministry Leader"
    And she should lose the basic "Member" permissions
    And the role change should be logged with timestamp

  @p0
  Scenario: Permission-based access control
    Given user "<EMAIL>" has "Volunteer" role
    And "Volunteer" role has limited permissions
    When Mike logs into the system
    Then he should only see features he has permission to access
    And he should see:
      | Feature              | Access Level |
      | Member Directory     | View only    |
      | Attendance          | No access    |
      | Reports             | No access    |
      | Organization Settings| No access    |
    And he should not see administrative features
    And attempts to access restricted features should be blocked

  @p1
  Scenario: Role-based navigation and UI
    Given user "<EMAIL>" has "Pastor" role
    When Pastor logs into the system
    Then the navigation menu should show pastor-appropriate options:
      | Menu Item           | Visible | Permissions           |
      | Dashboard          | Yes     | View ministry stats   |
      | Members            | Yes     | Full member management|
      | Attendance         | Yes     | View and manage       |
      | Services           | Yes     | Schedule and manage   |
      | Reports            | Yes     | Ministry reports      |
      | Settings           | Limited | Basic org settings    |
    And administrative features should be hidden or disabled

  @p1
  Scenario: Multiple role assignment
    Given user "<EMAIL>" needs multiple responsibilities
    When I assign multiple roles:
      | Role Name           | Department      | Reason                    |
      | Administrator       | All             | System management         |
      | Music Ministry Lead | Music Ministry  | Also leads worship team   |
    Then the user should have combined permissions from both roles
    And the system should handle permission conflicts appropriately
    And the user should see features from all assigned roles

  @p1
  Scenario: Temporary role assignment
    Given user "<EMAIL>" needs temporary access for an event
    When I assign a temporary role:
      | Field           | Value                    |
      | Role Name       | Event Coordinator        |
      | Start Date      | Today                    |
      | End Date        | Next Sunday              |
      | Reason          | Special event management |
    And I click "Assign Temporary Role"
    Then the user should gain the role permissions immediately
    And the role should automatically expire on the end date
    And the user should revert to their previous role
    And the temporary assignment should be logged

  @p2
  Scenario: Role hierarchy and inheritance
    Given I want to set up a role hierarchy:
      | Parent Role      | Child Roles              |
      | Administrator    | Pastor, Staff Member     |
      | Pastor           | Ministry Leader          |
      | Ministry Leader  | Volunteer               |
    When I configure role inheritance
    Then child roles should inherit parent permissions
    And additional permissions can be added to child roles
    And permission conflicts should be resolved by hierarchy
    And the hierarchy should be enforced in access control

  @p2
  Scenario: Permission audit and compliance
    Given I need to audit user permissions for compliance
    When I navigate to "Permission Audit"
    And I generate an audit report
    Then I should see a comprehensive report showing:
      | Information          | Details                           |
      | User Roles          | All users and their assigned roles|
      | Permission Matrix   | Roles and their specific permissions|
      | Access History      | Recent access attempts and results |
      | Role Changes        | History of role assignments       |
      | Compliance Status   | Any permission violations         |
    And I should be able to export the audit report

  @p1
  Scenario: Role deactivation and user impact
    Given role "Old Ministry Leader" is no longer needed
    And 3 users currently have this role
    When I navigate to "Role Management"
    And I select "Old Ministry Leader" role
    And I click "Deactivate Role"
    Then I should see a warning about affected users
    And I should be required to reassign affected users to new roles
    And the role should be deactivated only after all users are reassigned
    And the deactivation should be logged

  @p0
  Scenario: Security validation for role changes
    Given user "<EMAIL>" attempts to access role management
    When they try to change their own role to "Administrator"
    Then the system should reject the unauthorized change
    And a security alert should be generated
    And the attempt should be logged for audit
    And the user's account should be flagged for review
    And administrators should be notified of the security incident
