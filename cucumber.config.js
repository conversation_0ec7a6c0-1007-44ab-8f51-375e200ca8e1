module.exports = {
  default: {
    // Feature files location
    paths: ['features/**/*.feature'],
    
    // Step definitions location
    require: [
      'src/__tests__/cucumber/step-definitions/**/*.ts',
      'src/__tests__/cucumber/support/**/*.ts'
    ],
    
    // TypeScript support
    requireModule: ['ts-node/register'],
    
    // Format options
    format: [
      'progress-bar',
      'html:reports/cucumber/cucumber-report.html',
      'json:reports/cucumber/cucumber-report.json',
      '@cucumber/pretty-formatter'
    ],
    
    // Parallel execution
    parallel: 2,
    
    // Retry failed scenarios
    retry: 1,
    
    // Tags to run specific scenarios
    tags: process.env.CUCUMBER_TAGS || 'not @skip',
    
    // World parameters (shared data between steps)
    worldParameters: {
      baseUrl: process.env.BASE_URL || 'http://localhost:3000',
      timeout: 30000,
      headless: process.env.HEADLESS !== 'false'
    },
    
    // Publish results (set to false for local development)
    publish: false,
    
    // Exit on first failure in CI
    failFast: process.env.CI === 'true'
  },
  
  // Profile for smoke tests (P0 scenarios)
  smoke: {
    paths: ['features/**/*.feature'],
    require: [
      'src/__tests__/cucumber/step-definitions/**/*.ts',
      'src/__tests__/cucumber/support/**/*.ts'
    ],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber/smoke-report.html'
    ],
    tags: '@smoke and not @skip',
    parallel: 1,
    failFast: true
  },
  
  // Profile for P1 scenarios
  p1: {
    paths: ['features/**/*.feature'],
    require: [
      'src/__tests__/cucumber/step-definitions/**/*.ts',
      'src/__tests__/cucumber/support/**/*.ts'
    ],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber/p1-report.html'
    ],
    tags: '@p1 and not @skip',
    parallel: 2
  },
  
  // Profile for Africa-specific scenarios
  africa: {
    paths: ['features/**/*.feature'],
    require: [
      'src/__tests__/cucumber/step-definitions/**/*.ts',
      'src/__tests__/cucumber/support/**/*.ts'
    ],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber/africa-report.html'
    ],
    tags: '@africa and not @skip',
    parallel: 1,
    worldParameters: {
      baseUrl: process.env.BASE_URL || 'http://localhost:3000',
      timeout: 45000, // Longer timeout for low-bandwidth scenarios
      headless: true,
      slowMo: 100 // Slower execution for network-constrained environments
    }
  },
  
  // Profile for AI integration scenarios
  ai: {
    paths: ['features/**/*.feature'],
    require: [
      'src/__tests__/cucumber/step-definitions/**/*.ts',
      'src/__tests__/cucumber/support/**/*.ts'
    ],
    requireModule: ['ts-node/register'],
    format: [
      'progress-bar',
      'html:reports/cucumber/ai-report.html'
    ],
    tags: '@ai and not @skip',
    parallel: 1, // AI tests should run sequentially to avoid API rate limits
    worldParameters: {
      baseUrl: process.env.BASE_URL || 'http://localhost:3000',
      timeout: 60000, // Longer timeout for AI API calls
      headless: true
    }
  }
};
