#!/bin/bash

# CONFIGURATION
GITHUB_USER="jerryagenyi"
PROJECT_NUMBER=1
CSV_FILE="github-projects-import.csv"

# Get the project node ID
PROJECT_ID=$(gh api graphql -f query="
  query {
    user(login: \"$GITHUB_USER\") {
      projectV2(number: $PROJECT_NUMBER) {
        id
      }
    }
  }" --jq '.data.user.projectV2.id')

if [ -z "$PROJECT_ID" ]; then
  echo "Could not fetch project node ID. Check your username and project number."
  exit 1
fi

echo "Project node ID: $PROJECT_ID"

# Read the CSV and add each item as a draft issue
tail -n +2 "$CSV_FILE" | while IFS=, read -r Title Status Milestone Labels Notes; do
  # Remove quotes if present
  Title=$(echo $Title | sed 's/^"//;s/"$//')
  Notes=$(echo $Notes | sed 's/^"//;s/"$//')
  # Only add items that are not already marked as Done
  if [[ "$Status" != "Done" ]]; then
    echo "Adding: $Title"
    gh api graphql -f query="
      mutation {
        addProjectV2DraftIssue(input: {
          projectId: \"$PROJECT_ID\"
          title: \"$Title\"
          body: \"$Notes\"
        }) {
          projectItem {
            id
          }
        }
      }"
    sleep 1 # To avoid hitting rate limits
  fi
done

echo "All items added to GitHub Project!"