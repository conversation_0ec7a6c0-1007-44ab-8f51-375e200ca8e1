import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { CustomWorld } from '../support/world';
import { screen, waitFor } from '@testing-library/react';

// Authentication steps
Given('I am logged in as a {string}', async function(this: CustomWorld, userType: string) {
  await this.loginAs(userType as 'admin' | 'pastor' | 'member' | 'visitor');
});

Given('I am not logged in', async function(this: CustomWorld) {
  this.currentUser = undefined;
  if (this.page) {
    await this.page.goto(`${this.parameters.baseUrl}/logout`);
  }
});

Given('I have permissions to {string}', async function(this: CustomWorld, permission: string) {
  // Mock permission check - in real implementation, this would verify user permissions
  this.testData.permissions = this.testData.permissions || [];
  this.testData.permissions.push(permission);
});

// Navigation steps
When('I navigate to {string}', async function(this: CustomWorld, path: string) {
  if (this.page) {
    await this.navigateTo(path);
    await this.waitForPageLoad();
  }
});

When('I navigate to {string} > {string}', async function(this: CustomWorld, section: string, subsection: string) {
  if (this.page) {
    // Navigate through menu structure
    await this.page.click(`[data-testid="nav-${section.toLowerCase().replace(/\s+/g, '-')}"]`);
    await this.page.click(`[data-testid="nav-${subsection.toLowerCase().replace(/\s+/g, '-')}"]`);
    await this.waitForPageLoad();
  }
});

When('I click {string}', async function(this: CustomWorld, buttonText: string) {
  if (this.page) {
    const selector = `button:has-text("${buttonText}"), [data-testid="${buttonText.toLowerCase().replace(/\s+/g, '-')}"]`;
    await this.page.click(selector);
  }
});

// Form interaction steps
When('I enter {string} in the {string} field', async function(this: CustomWorld, value: string, fieldName: string) {
  if (this.page) {
    const selector = `[data-testid="${fieldName}"], [name="${fieldName}"], input[placeholder*="${fieldName}"]`;
    await this.page.fill(selector, value);
  }
});

When('I select {string} from the {string} dropdown', async function(this: CustomWorld, option: string, fieldName: string) {
  if (this.page) {
    const selector = `[data-testid="${fieldName}"], select[name="${fieldName}"]`;
    await this.page.selectOption(selector, option);
  }
});

When('I enter the following details:', async function(this: CustomWorld, dataTable) {
  const data = dataTable.rowsHash();
  await this.fillForm(data);
});

When('I fill in the form with:', async function(this: CustomWorld, dataTable) {
  const data = dataTable.rowsHash();
  await this.fillForm(data);
});

// Verification steps
Then('I should see {string}', async function(this: CustomWorld, text: string) {
  if (this.page) {
    await expect(this.page.locator(`text=${text}`)).toBeVisible();
  } else if (this.renderResult) {
    await waitFor(() => {
      expect(screen.getByText(text)).toBeInTheDocument();
    });
  }
});

Then('I should not see {string}', async function(this: CustomWorld, text: string) {
  if (this.page) {
    await expect(this.page.locator(`text=${text}`)).not.toBeVisible();
  } else if (this.renderResult) {
    expect(screen.queryByText(text)).not.toBeInTheDocument();
  }
});

Then('I should be on the {string} page', async function(this: CustomWorld, pageName: string) {
  if (this.page) {
    const expectedPath = `/${pageName.toLowerCase().replace(/\s+/g, '-')}`;
    await expect(this.page).toHaveURL(new RegExp(expectedPath));
  }
});

Then('the page title should be {string}', async function(this: CustomWorld, title: string) {
  if (this.page) {
    await expect(this.page).toHaveTitle(title);
  }
});

// Table verification steps
Then('I should see a table with the following data:', async function(this: CustomWorld, dataTable) {
  const expectedData = dataTable.raw();
  
  if (this.page) {
    const actualData = await this.getTableData();
    
    // Compare headers
    const expectedHeaders = expectedData[0];
    const actualHeaders = actualData[0];
    expect(actualHeaders).toEqual(expectedHeaders);
    
    // Compare data rows
    for (let i = 1; i < expectedData.length; i++) {
      expect(actualData[i]).toEqual(expectedData[i]);
    }
  }
});

Then('the table should contain {int} rows', async function(this: CustomWorld, expectedRowCount: number) {
  if (this.page) {
    const tableData = await this.getTableData();
    expect(tableData.length - 1).toBe(expectedRowCount); // Subtract 1 for header row
  }
});

// Form validation steps
Then('I should see a validation error {string}', async function(this: CustomWorld, errorMessage: string) {
  if (this.page) {
    await expect(this.page.locator('.error, [role="alert"]').filter({ hasText: errorMessage })).toBeVisible();
  }
});

Then('the form should be submitted successfully', async function(this: CustomWorld) {
  if (this.page) {
    // Wait for success message or redirect
    await Promise.race([
      this.page.waitForSelector('.success, [data-testid="success-message"]'),
      this.page.waitForURL(/.*\/success|.*\/dashboard/)
    ]);
  }
});

// Loading and state steps
Then('I should see a loading indicator', async function(this: CustomWorld) {
  if (this.page) {
    await expect(this.page.locator('[data-testid="loading"], .loading, .spinner')).toBeVisible();
  }
});

Then('the loading indicator should disappear', async function(this: CustomWorld) {
  if (this.page) {
    await expect(this.page.locator('[data-testid="loading"], .loading, .spinner')).not.toBeVisible();
  }
});

// Modal and dialog steps
Then('I should see a modal with title {string}', async function(this: CustomWorld, title: string) {
  if (this.page) {
    await expect(this.page.locator('[role="dialog"]')).toBeVisible();
    await expect(this.page.locator('[role="dialog"] h1, [role="dialog"] h2').filter({ hasText: title })).toBeVisible();
  }
});

When('I close the modal', async function(this: CustomWorld) {
  if (this.page) {
    await this.page.click('[role="dialog"] button[aria-label="Close"], [role="dialog"] .close-button');
  }
});

// File upload steps
When('I upload a file {string}', async function(this: CustomWorld, fileName: string) {
  if (this.page) {
    const filePath = `src/__tests__/fixtures/${fileName}`;
    await this.page.setInputFiles('input[type="file"]', filePath);
  }
});

// Wait steps
When('I wait for {int} seconds', async function(this: CustomWorld, seconds: number) {
  await this.waitFor(seconds * 1000);
});

When('I wait for the page to load', async function(this: CustomWorld) {
  await this.waitForPageLoad();
});

// System state steps
Given('the system is configured for {string}', async function(this: CustomWorld, configuration: string) {
  this.testData.systemConfig = configuration;
  
  if (configuration === 'Africa') {
    this.testData.features = {
      whatsapp: true,
      sms: true,
      mobileMoneyPayments: true,
      offlineMode: true,
      multiLanguage: true
    };
  }
});

Given('the database contains test data', async function(this: CustomWorld) {
  // This step is handled by the @database hook
  // Just verify that test data exists
  expect(this.testData).toBeDefined();
});

// Error handling steps
Then('I should see an error message', async function(this: CustomWorld) {
  if (this.page) {
    await expect(this.page.locator('.error, [role="alert"], [data-testid="error-message"]')).toBeVisible();
  }
});

Then('I should see a success message', async function(this: CustomWorld) {
  if (this.page) {
    await expect(this.page.locator('.success, [data-testid="success-message"]')).toBeVisible();
  }
});

// Responsive design steps
Given('I am using a {string} device', async function(this: CustomWorld, deviceType: string) {
  if (this.page) {
    const viewports = {
      mobile: { width: 375, height: 667 },
      tablet: { width: 768, height: 1024 },
      desktop: { width: 1280, height: 720 }
    };
    
    const viewport = viewports[deviceType as keyof typeof viewports];
    if (viewport) {
      await this.page.setViewportSize(viewport);
    }
  }
});
