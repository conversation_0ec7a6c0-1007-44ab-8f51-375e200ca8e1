@mvp @p1 @member-management
Feature: Member Profile Management
  As a Church Staff Member
  I want to manage member profiles and information
  So that I can maintain accurate records and provide personalized ministry

  Background:
    Given I am logged in as a Church Staff Member
    And member profiles exist in the system
    And I have appropriate permissions to manage members

  @p1 @smoke
  Scenario: View member profile details
    Given a member "<PERSON>" exists with complete profile information
    When I navigate to <PERSON>'s profile page
    Then I should see all member details:
      | Field            | Value                |
      | Full Name        | <PERSON>             |
      | Email            | <EMAIL>       |
      | Phone            | +1234567890          |
      | Date of Birth    | January 15, 1990     |
      | Address          | 123 Main St          |
      | Membership Status| Active               |
      | Join Date        | January 1, 2023      |
    And I should see the member's attendance history
    And I should see family connections if any exist

  @p1
  Scenario: Edit member profile information
    Given I am viewing <PERSON>'s profile
    When I click the "Edit Profile" button
    And I update the phone number to "+1987654321"
    And I update the address to "456 Oak Avenue"
    And I click "Save Changes"
    Then the profile should be updated successfully
    And I should see "Profile updated successfully"
    And the new information should be displayed
    And the changes should be logged in the audit trail

  @p1
  Scenario: Add member profile photo
    Given I am editing a member's profile
    When I click "Upload Photo"
    And I select a valid image file (JPG, PNG, or GIF)
    And the file is under 5MB
    And I click "Save"
    Then the photo should be uploaded successfully
    And the photo should appear on the member's profile
    And the photo should be resized appropriately for display

  @p1
  Scenario: Update membership status
    Given I am viewing a member's profile
    And I have permission to change membership status
    When I click "Change Status"
    And I select "Inactive" from the status dropdown
    And I provide a reason "Moved to another city"
    And I click "Update Status"
    Then the membership status should be changed to "Inactive"
    And the reason should be recorded
    And the change should be logged with timestamp and user

  @p1
  Scenario: Add member notes and ministry involvement
    Given I am editing a member's profile
    When I add a note "Interested in youth ministry leadership"
    And I select ministry involvement "Youth Ministry"
    And I set the involvement level to "Leader"
    And I click "Save Changes"
    Then the note should be saved to the member's profile
    And the ministry involvement should be recorded
    And the information should be visible to authorized staff

  @p0
  Scenario: Member profile data validation
    Given I am editing a member's profile
    When I enter an invalid email format "invalid-email"
    And I enter an invalid phone number "abc123"
    And I click "Save Changes"
    Then I should see validation errors
    And the error should specify "Please enter a valid email address"
    And the error should specify "Please enter a valid phone number"
    And the profile should not be saved

  @p1
  Scenario: Search and filter member profiles
    Given multiple members exist in the system
    When I navigate to the member directory
    And I search for "John"
    Then I should see all members with "John" in their name
    When I filter by "Active" membership status
    Then I should only see active members named John
    When I filter by "Youth Ministry" involvement
    Then I should see John if he's involved in Youth Ministry

  @p1
  Scenario: Member profile privacy settings
    Given I am viewing a member's profile
    And the member has set privacy preferences
    When I try to view sensitive information
    Then I should only see information I'm authorized to view
    And restricted information should show "Access Restricted"
    And I should see a note about privacy settings

  @p2
  Scenario: Export member profile data
    Given I am viewing a member's profile
    And I have export permissions
    When I click "Export Profile"
    And I select "PDF" format
    Then a PDF file should be generated with the member's information
    And the PDF should include profile photo if available
    And the export should be logged for audit purposes

  @p2
  Scenario: Member profile history and audit trail
    Given I am viewing a member's profile
    When I click "View History"
    Then I should see a chronological list of all profile changes
    And each change should show:
      | Field Changed | Old Value | New Value | Changed By | Date/Time |
    And I should be able to filter history by date range
    And I should be able to filter by type of change

  @p2
  Scenario: Merge duplicate member profiles
    Given two member profiles exist for the same person
    And I have administrative permissions
    When I identify the duplicate profiles
    And I click "Merge Profiles"
    And I select which information to keep from each profile
    And I confirm the merge
    Then the profiles should be combined into one
    And all historical data should be preserved
    And references from other records should be updated
