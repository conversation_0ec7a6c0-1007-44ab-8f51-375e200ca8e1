#!/usr/bin/env node

/**
 * Test script to verify Cucumber.js setup
 * This script checks if all dependencies and configurations are properly set up
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🥒 Testing Cucumber.js Setup...\n');

// Check if required files exist
const requiredFiles = [
  'cucumber.config.js',
  'src/__tests__/cucumber/support/world.ts',
  'src/__tests__/cucumber/support/hooks.ts',
  'src/__tests__/cucumber/step-definitions/common-steps.ts',
  'src/__tests__/cucumber/step-definitions/authentication-steps.ts',
  'src/__tests__/cucumber/step-definitions/member-management-steps.ts',
  'features/authentication/login.feature',
  'features/member-management/member-profiles.feature'
];

console.log('📁 Checking required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing. Please ensure all files are created.');
  process.exit(1);
}

// Check package.json scripts
console.log('\n📦 Checking package.json scripts...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredScripts = [
  'cucumber',
  'cucumber:smoke',
  'cucumber:p1',
  'cucumber:africa',
  'cucumber:ai',
  'test:bdd'
];

requiredScripts.forEach(script => {
  if (packageJson.scripts[script]) {
    console.log(`✅ ${script}: ${packageJson.scripts[script]}`);
  } else {
    console.log(`❌ ${script} - MISSING`);
  }
});

// Check dependencies
console.log('\n📚 Checking dependencies...');
const requiredDeps = [
  '@cucumber/cucumber',
  '@cucumber/pretty-formatter',
  '@cucumber/html-formatter',
  'ts-node',
  'playwright'
];

const devDeps = packageJson.devDependencies || {};
requiredDeps.forEach(dep => {
  if (devDeps[dep]) {
    console.log(`✅ ${dep}: ${devDeps[dep]}`);
  } else {
    console.log(`❌ ${dep} - MISSING`);
  }
});

// Create reports directory if it doesn't exist
const reportsDir = 'reports/cucumber';
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
  console.log(`\n📁 Created reports directory: ${reportsDir}`);
}

// Test TypeScript compilation
console.log('\n🔧 Testing TypeScript compilation...');
try {
  execSync('npx tsc --noEmit --project tsconfig.json', { stdio: 'pipe' });
  console.log('✅ TypeScript compilation successful');
} catch (error) {
  console.log('⚠️  TypeScript compilation warnings (this is normal for test files)');
}

// Test Cucumber configuration
console.log('\n⚙️  Testing Cucumber configuration...');
try {
  const config = require(path.join(process.cwd(), 'cucumber.config.js'));
  console.log('✅ Cucumber configuration loaded successfully');
  console.log(`   - Default profile configured: ${!!config.default}`);
  console.log(`   - Smoke profile configured: ${!!config.smoke}`);
  console.log(`   - Africa profile configured: ${!!config.africa}`);
  console.log(`   - AI profile configured: ${!!config.ai}`);
} catch (error) {
  console.log(`❌ Cucumber configuration error: ${error.message}`);
}

// Test feature file parsing
console.log('\n🥒 Testing feature file parsing...');
try {
  const featureFiles = [
    'features/authentication/login.feature',
    'features/member-management/member-profiles.feature'
  ];
  
  featureFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('Feature:') && content.includes('Scenario:')) {
        console.log(`✅ ${file} - Valid Gherkin syntax`);
      } else {
        console.log(`⚠️  ${file} - May have syntax issues`);
      }
    }
  });
} catch (error) {
  console.log(`❌ Feature file parsing error: ${error.message}`);
}

console.log('\n🎉 Cucumber.js setup verification complete!');
console.log('\n📋 Next steps:');
console.log('   1. Run smoke tests: npm run cucumber:smoke');
console.log('   2. Run all BDD tests: npm run test:bdd');
console.log('   3. View reports in: reports/cucumber/');
console.log('   4. Add more step definitions as needed');

console.log('\n💡 Useful commands:');
console.log('   - npm run cucumber:smoke    # Run P0 scenarios');
console.log('   - npm run cucumber:p1       # Run P1 scenarios');
console.log('   - npm run cucumber:africa   # Run Africa-specific tests');
console.log('   - npm run cucumber:ai       # Run AI integration tests');
console.log('   - npm run test:all          # Run Vitest + Cucumber smoke tests');
