# ChMS Documentation Guide

This document provides an overview of the documentation structure for the Church Management System (ChMS) project.

## Documentation Structure

The project documentation is organized into two main directories:

### 1. Application & Developer Documentation (`docs/`)

The `docs/` directory contains documentation related to the **built ChMS application** and the **development process**. This includes:

- **User Guides**: Instructions for end-users on how to use the application
- **API Documentation**: Details about the application's API endpoints
- **Development Standards**: Coding, testing, and documentation standards
- **Tutorials**: Learning materials for developers

[Browse the docs/ directory →](./docs/)

### 2. Project Management Documentation (`pm/`)

The `pm/` directory contains **project planning**, **requirements**, and **management artifacts**. This includes:

- **Product Requirements**: Goals, scope, and high-level features
- **Technical Architecture**: System design and component structure
- **User Stories**: User-centered requirements
- **Feature Requirements**: Detailed specifications for individual features
- **Security Documentation**: Security policies and procedures

[Browse the pm/ directory →](./pm/)

### 3. Contributing Guidelines

The `CONTRIBUTING.md` file at the root level provides instructions for setting up the development environment and contributing to the project.

[View Contributing Guidelines →](./CONTRIBUTING.md)

## Document Versioning

All documentation in this project follows semantic versioning (MAJOR.MINOR.PATCH):

- **MAJOR:** Significant changes that fundamentally alter the document's purpose or structure
- **MINOR:** New sections or substantial updates to existing content
- **PATCH:** Minor updates, corrections, or clarifications

Each document includes a version history section at the end to track changes.

## Quick Links

### Key Project Management Documents

- [Product Requirements Document](./pm/prd.md)
- [Technical Architecture](./pm/technical-architecture.md)
- [User Stories](./pm/user-stories.md)
- [Security Policy](./pm/security-policy.md)

### Key Application Documentation

- [User Guide](./docs/user-guide.md)
- [API Documentation](./docs/api-documentation.md)
- [Development Standards](./docs/standards/development-standards.md)
- [Testing Standards](./docs/standards/testing-standards.md)

## Version History

### 1.0.0 - [Current Date]
- Initial creation of the documentation guide
