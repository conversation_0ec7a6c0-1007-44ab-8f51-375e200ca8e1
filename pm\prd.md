# Product Requirements Document (PRD) - ChMS

---

## 🌍 West Africa Context

ChMS is designed specifically for the realities of West Africa. This context shapes every technical and product decision:

| Area                | Consideration                                                                 |
|---------------------|-------------------------------------------------------------------------------|
| Offline-first       | Cache data, background sync, defer heavy AI to backend                        |
| Device compatibility| Prioritize Android, mobile-first, avoid heavy JS                              |
| Payment methods     | Integrate Flutterwave, Paystack, Momo                                         |
| Language/locale     | Support Pidgin, Hausa, Yoruba, French, etc.                                   |
| USSD/WhatsApp       | Consider WhatsApp bot/USSD for rural access                                   |
| Data sync queues    | Log attendance offline, sync later                                            |
| Lite/full version   | Offer "Lite" UI for low-end devices                                           |

These priorities are reflected throughout the MVP, technical stack, and roadmap. Features and tasks influenced by these constraints are labeled `africa-priority` and/or `low-bandwidth` in the project board.

---

## ✅ TL;DR

ChMS is an AI-native, low-resource church management system (ChMS) built for Africa. It starts with attendance and member tracking and grows into a full platform for church operations.

- 🌍 Built for low-connectivity environments in West Africa
- 🧠 AI-native using Flowise agents + LLMs
- 📱 Mobile-first, offline-capable
- 🆓 Free core, with optional donations/ads/premium features

---

## 1. Introduction & Purpose

ChMS (Church Management System) is a comprehensive solution designed specifically for the needs of African churches. The primary purpose is to provide an easy-to-use, reliable, and accessible system for managing church operations, focusing on scalability, offline-first functionality, and optimization for low-bandwidth environments.

**Target Audience:** Churches in Africa, potentially with limited or intermittent internet connectivity.

**Vision:**
To empower every church in Africa — rural or urban — to manage their members, attendance, and growth using accessible technology. No cost. No complexity. No compromise.

**Goals:**

- **Scalable and Maintainable:** Built with modern best practices to support growth.
- **Offline-First & Low-Bandwidth Optimized:** Core functionality should work reliably with poor connectivity.
- **Accessible and User-Friendly:** Intuitive interface for users with varying technical skills.
- **Well-documented and Testable:** Ensure ease of maintenance and future development.
- **AI-Native from Day One:** Leverage Flowise, n8n, and LLMs for onboarding, automation, and insights.
- **Africa-First:** Prioritize local context, languages, and connectivity realities.

---

## 2. Features Overview

- **Foundation:**
  - Project Setup & Core Components
  - Authentication & Authorization (Role-Based Access Control)
  - Basic Routing
  - Database Setup
- **Core Features:**
  - Organization Management
  - Member Management (including Family Units)
  - Attendance System (QR/Face check-in, tagging, summaries, WhatsApp/USSD fallback, offline queue)
  - Basic Reporting
- **AI-Native Features:**
  - Flowise-powered onboarding and conversational flows
  - n8n event automation (e.g., alerts, reminders)
  - AI-generated attendance summaries and insights (server-side for low-end devices)
  - Natural language queries ("Show me all absentees in March")
  - AI-powered language simplification/translation (e.g., Pidgin summaries)
- **Advanced Features (Future):**
  - Communication System (Email, SMS, WhatsApp)
  - Advanced Reporting & Analytics
  - Event Management
  - Visitor Management
  - Mobile Optimization / PWA features
  - Voice input, WhatsApp bot
  - Media integration (sermon transcripts, voice-to-text)
  - Lite/full version toggle

---

## 3. User Needs & User Stories

_(Link to or summarize key user stories from `pm/user-stories.md`)_

| User            | Description |
|-----------------|-------------|
| Church Admin    | Manages church data, attendance, people |
| Group Leader    | Handles events and attendance by role/group |
| Church Member   | Checks in to events, views their own record |
| Volunteer Agent | Helps new churches onboard using a chatbot |
| Low-Tech Church | Needs offline-first, mobile access, WhatsApp/USSD fallback |

**Example Stories:**
- A church administrator needs to easily add, view, and update member profiles, even when offline.
- A pastor needs to quickly record attendance for a service using a mobile device or WhatsApp.
- A volunteer agent uses an AI chatbot to onboard a new church.
- A treasurer needs access to basic financial reports based on member contributions (if applicable).
- A member in a rural area needs to check in via WhatsApp or USSD when internet is unavailable.

---

## 4. Technical Requirements

- **Stack:**
  - Frontend: Next.js 15 + Chakra UI
  - Backend/API: Next.js API Routes/Server Actions
  - Database: Supabase (PostgreSQL)
  - ORM: Prisma
  - Auth: NextAuth.js
  - AI Integration: Flowise, n8n, Ollama, OpenAI/Claude fallback
  - State/Data: SWR, React Hook Form, Zod
- **Performance:** Fast load times (<2s), offline capability, low bandwidth usage, Lite mode for low-end devices.
- **Security:** RBAC, data encryption, input validation, standard web security practices.
- **Testing:** Unit, integration, E2E tests with high coverage (see `pm/testing-strategy.md`).
- **Offline:** SWR cache + background sync (planned), data sync queues for attendance.
- **Device Compatibility:** Mobile-first, Android-priority, avoid heavy JS, Lite mode.
- **Payments:** Integrate Flutterwave, Paystack, Momo (future).
- **Language/Locale:** Multi-language-ready (Pidgin, Hausa, Yoruba, French, etc.).

---

## 5. Design & UI/UX

- Utilizes Chakra UI component library.
- Responsive design for various screen sizes.
- Dark mode support.
- Accessibility compliance (WCAG).
- Minimalist and intuitive user interface.
- Mobile-first, Africa-first design.
- Lite/full version toggle for low-end devices.

---

## 6. Success Metrics

- Number of churches onboarded
- Daily/weekly attendance submissions
- Offline sync success rate
- User satisfaction (surveys)
- Agent-led onboarding conversions
- System uptime and performance benchmarks
- Usage of WhatsApp/USSD fallback and Lite mode

---

## 7. AI-Native Design

| Tool | Purpose |
|------|---------|
| **Flowise** | Visual LLM workflows (chatbot setup, data queries, onboarding) |
| **Ollama / GPT-4o / Claude** | AI reasoning, summaries, user queries (server-side for low-end devices) |
| **pgvector / embeddings** | Search, similarity-based member discovery |
| **n8n** | Event automation (e.g., low attendance alerts) |

**Example Use Cases:**
- Smart onboarding (AI agents / forms)
- Automated engagement (reminders, re-engagement)
- Conversational interface for setup and reporting
- AI-powered language simplification/translation (e.g., Pidgin summaries)
- Server-side AI for low-resource devices

---

## 8. Tradeoffs & Open Questions

| Decision                     | Alternatives |
|-----------------------------|--------------|
| AI-first onboarding         | Static form, manual entry |
| Face recognition for check-in | QR code fallback, WhatsApp/USSD fallback |
| Free core features          | Premium tier, pay-per-feature |
| Donation/ad model           | Paywall or enterprise sales |
| Lite/full version           | Single UI for all devices |

**Open Questions:**
- Which offline sync method is most reliable (Service Workers, SWR + local queue)?
- How do we handle facial recognition legally and ethically?
- Can Flowise agents be embedded into mobile UIs effectively?
- Should we expose public APIs for integration?
- How to best support WhatsApp/USSD fallback for rural churches?
- How to optimize Lite mode for low-end devices?
- [Mark areas needing feedback here]

---

## 9. Roadmap

| Version | Features |
|---------|----------|
| **v0.1** | MVP: attendance (QR/Face/WhatsApp/USSD), onboarding, AI agents, offline queue, Lite mode |
| **v0.2** | Dashboards, reminders, offline sync, multi-language support |
| **v1.0** | Member search, analytics, payments, monetization |
| **v2.0+** | Voice input, full CMS, WhatsApp bot, media integration |

---

## 10. Monetization Strategy

- **Always free** for core features (attendance, members, onboarding)
- **Optional ads**, with toggle control
- **Donations** from churches and sponsors
- **Premium tier** for AI-powered features or branding

---

## 11. Documentation & Collaboration

- PRD maintained in this repo (`pm/prd.md`)
- Collaborative editing via Google Docs/Notion as needed
- Feedback and open questions tracked in this document

---

## 12. Feature-to-Context Mapping

| Epic/Feature                | West Africa Consideration(s)         | Label(s)           |
|-----------------------------|--------------------------------------|--------------------|
| Attendance                  | Offline, WhatsApp/USSD, Lite mode    | africa-priority    |
| Payments                    | Flutterwave, Paystack, Momo          | africa-priority    |
| Language/Locale             | Pidgin, Hausa, Yoruba, French        | africa-priority    |
| AI                          | Server-side, summarization, voice    | africa-priority    |
| Device Compatibility        | Android, low-end, mobile-first       | low-bandwidth      |

---

**Created by:** [Your Name / Org]
**Maintained on:** GitHub Projects (Beta)
**Tools in use:** Notion, Google Docs, Flowise, Supabase
