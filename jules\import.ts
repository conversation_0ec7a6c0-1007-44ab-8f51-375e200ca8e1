import { NextApiRequest, NextApiResponse } from 'next';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/services/auth-options'; // Assuming this path is correct
import { IncomingForm, File } from 'formidable';
import fs from 'fs';
import { parse } from 'csv-parse';

// Define the Zod schema for a member record from CSV
// Looser validation for CSV, actual transformation/validation before DB insert
const csvMemberSchema = z.object({
  email: z.string().email({ message: "Invalid email format" }),
  name: z.string().min(1, { message: "Name cannot be empty" }),
  firstName: z.string().optional(), // Will be derived from name if not provided
  lastName: z.string().optional(), // Will be derived from name if not provided
  role: z.string().optional(), // Assuming 'role' might be in CSV, map to User.role or Member field
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(), // Expecting string, will parse to Date
  address: z.string().optional(),
  // Add any other fields expected from the CSV template in ImportUsers.tsx
  department: z.string().optional(), 
});

// Define the expected CSV headers based on ImportUsers.tsx template
// "email,name,role,department,phoneNumber,dateOfBirth,address"
const EXPECTED_CSV_HEADERS = [
  "email", "name", "role", "department", "phoneNumber", "dateOfBirth", "address"
];

export const config = {
  api: {
    bodyParser: false, // Disable Next.js default body parser
  },
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session || !session.user || !session.user.organizationId) {
    return res.status(401).json({ error: 'Unauthorized: No session or organizationId' });
  }
  const { organizationId } = session.user;

  const form = new IncomingForm();

  form.parse(req, async (err, fields, files) => {
    if (err) {
      console.error('Error parsing form:', err);
      return res.status(500).json({ error: 'Error processing file upload' });
    }

    const file = files.file;

    if (!file || (Array.isArray(file) && file.length === 0)) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const uploadedFile = Array.isArray(file) ? file[0] : file;

    if (!uploadedFile.mimetype || !uploadedFile.mimetype.includes('csv')) {
        return res.status(400).json({ error: 'Invalid file type. Only CSV is allowed.' });
    }

    const results = {
      successful: 0,
      failed: 0,
      errors: [] as string[],
      membersToCreate: [] as any[], // Store valid member data for transaction
    };

    const parser = fs
      .createReadStream(uploadedFile.filepath)
      .pipe(parse({ 
        columns: true, 
        skip_empty_lines: true,
        trim: true,
        // Explicitly define columns if headers might be inconsistent or to enforce order
        // columns: EXPECTED_CSV_HEADERS, 
        // from_line: 2, // if headers are present and should be skipped by columns:true
      }));

    let rowNumber = 1; // Start from 1 for header row, or 2 if skipping header manually

    // Check headers
    let headersChecked = false;

    for await (const record of parser) {
      if (!headersChecked) {
        const actualHeaders = Object.keys(record);
        const missingHeaders = EXPECTED_CSV_HEADERS.filter(h => !actualHeaders.includes(h));
        if (missingHeaders.length > 0) {
          // Allow for 'phoneNumber' vs 'phone' flexibility if needed, or be strict
          // For now, being strict with the template's "phoneNumber"
          results.errors.push(`CSV Header mismatch. Missing: ${missingHeaders.join(', ')}. Please use the template.`);
          // Abort further processing by breaking the loop
          // To make this more robust, you might consume the rest of the stream to prevent hanging
          parser.destroy(); // Stop reading the file
          break; 
        }
        headersChecked = true;
      }
      
      rowNumber++;
      const validationResult = csvMemberSchema.safeParse({
        ...record,
        phone: record.phoneNumber || record.phone, // Handle variation
      });

      if (validationResult.success) {
        const data = validationResult.data;
        let { firstName, lastName } = data;

        if (!firstName && data.name) {
          const nameParts = data.name.trim().split(/\s+/);
          firstName = nameParts[0];
          lastName = nameParts.slice(1).join(' ') || undefined; // Handle single names
        }
        
        // Basic transformation for dateOfBirth
        let dob: Date | null = null;
        if (data.dateOfBirth) {
            try {
                dob = new Date(data.dateOfBirth);
                if (isNaN(dob.getTime())) { // Invalid date
                    results.failed++;
                    results.errors.push(`Row ${rowNumber}: Invalid date format for dateOfBirth: ${data.dateOfBirth}`);
                    continue;
                }
            } catch (e) {
                results.failed++;
                results.errors.push(`Row ${rowNumber}: Could not parse dateOfBirth: ${data.dateOfBirth}`);
                continue;
            }
        }

        results.membersToCreate.push({
          email: data.email,
          firstName: firstName,
          lastName: lastName,
          phone: data.phone,
          organizationId: organizationId,
          // Map 'role' from CSV if necessary. Prisma Member model has no 'role'.
          // 'department' from CSV is also not directly on Member model. Store in notes or custom field if needed.
          // For now, these are ignored unless Prisma schema is extended or they map to e.g. User.role
          dateOfBirth: dob,
          // address: data.address, // address is not on Member model, maybe on User or a Profile model
        });
      } else {
        results.failed++;
        const errorMessages = validationResult.error.errors.map(e => `Row ${rowNumber} (${record.email || 'N/A'}): ${e.path.join('.')} - ${e.message}`).join('; ');
        results.errors.push(errorMessages);
      }
    }
    
    // If header check failed and loop was broken, return early
    if (results.errors.some(e => e.startsWith("CSV Header mismatch"))) {
        // Clean up the uploaded file
        fs.unlink(uploadedFile.filepath, (unlinkErr) => {
            if (unlinkErr) console.error("Error deleting temp file:", unlinkErr);
        });
        return res.status(400).json({
            successful: 0,
            failed: rowNumber -1, // or total rows if known
            errors: results.errors,
        });
    }


    if (results.membersToCreate.length > 0) {
      try {
        // Using Prisma transaction to create members
        // Note: $transaction with array of create operations might be slow for very large CSVs.
        // Consider createMany if your DB supports it and you don't need results of each create.
        // For now, this approach is fine and gives individual error potential if not for transactions.
        // However, with $transaction, if one fails, all fail.
        // A more robust batching strategy might be needed for very large files (e.g., 1000s of rows)
        
        // Let's refine to create members one by one to report individual successes/failures
        // Or use createMany and accept that it's all or nothing for the batch.
        // For better error reporting per row during DB insert:
        for (const memberData of results.membersToCreate) {
            try {
                await prisma.member.create({ data: memberData });
                results.successful++;
            } catch (dbError: any) {
                results.failed++;
                let errorMessage = `Row (Email: ${memberData.email}): Database error.`;
                if (dbError.code === 'P2002' && dbError.meta?.target?.includes('email')) {
                    errorMessage = `Row (Email: ${memberData.email}): Email already exists.`;
                }
                results.errors.push(errorMessage);
            }
        }
        
      } catch (dbError: any) {
        // This catch block might not be hit if individual creates are handled above.
        // If $transaction was used and failed, this would be the place.
        console.error('Database transaction error:', dbError);
        results.errors.push('A database error occurred during bulk import. Some records may not have been saved.');
        // Adjust successful/failed counts if the transaction rolls back everything
        // For now, individual try-catch handles this better for per-row feedback.
      }
    }

    // Clean up the uploaded file
    fs.unlink(uploadedFile.filepath, (unlinkErr) => {
        if (unlinkErr) console.error("Error deleting temp file:", unlinkErr);
    });

    return res.status(200).json({
      successful: results.successful,
      failed: results.failed,
      errors: results.errors,
    });
  });
}
