@mvp @p0 @authentication
Feature: User Authentication System
  As a Church Staff Member
  I want to securely log into the ChMS system
  So that I can access church management features based on my role

  Background:
    Given the ChMS application is running
    And the authentication system is configured
    And user accounts exist in the system

  @smoke
  Scenario: Successful login with valid credentials
    Given I am on the login page
    When I enter valid email "<EMAIL>"
    And I enter valid password "SecurePass123!"
    And I click the "Sign In" button
    Then I should be redirected to the dashboard
    And I should see a welcome message
    And my session should be established
    And I should see navigation options based on my role

  @p0
  Scenario: Failed login with invalid email
    Given I am on the login page
    When I enter invalid email "<EMAIL>"
    And I enter any password "password123"
    And I click the "Sign In" button
    Then I should see an error message "Invalid credentials"
    And I should remain on the login page
    And no session should be established

  @p0
  Scenario: Failed login with invalid password
    Given I am on the login page
    When I enter valid email "<EMAIL>"
    And I enter invalid password "wrongpassword"
    And I click the "Sign In" button
    Then I should see an error message "Invalid credentials"
    And I should remain on the login page
    And no session should be established

  @p0
  Scenario: Login form validation
    Given I am on the login page
    When I leave the email field empty
    And I leave the password field empty
    And I click the "Sign In" button
    Then I should see validation errors
    And the "Email is required" message should be displayed
    And the "Password is required" message should be displayed
    And the sign in button should remain disabled

  @p0
  Scenario: Login with malformed email
    Given I am on the login page
    When I enter malformed email "invalid-email"
    And I enter any password "password123"
    And I click the "Sign In" button
    Then I should see validation error "Please enter a valid email address"
    And the form should not be submitted

  @p1
  Scenario: Remember me functionality
    Given I am on the login page
    When I enter valid credentials
    And I check the "Remember me" checkbox
    And I click the "Sign In" button
    Then I should be logged in successfully
    And my session should persist beyond browser closure
    And I should remain logged in on return visits

  @p1
  Scenario: Logout functionality
    Given I am logged into the system
    When I click the "Logout" button
    Then I should be redirected to the login page
    And my session should be terminated
    And I should not be able to access protected pages
    And any "Remember me" settings should be cleared

  @p1
  Scenario: Session timeout handling
    Given I am logged into the system
    And my session has been idle for the timeout period
    When I try to access any protected page
    Then I should be redirected to the login page
    And I should see a message "Your session has expired"
    And I should need to log in again to continue

  @p1
  Scenario: Concurrent session handling
    Given I am logged into the system on one device
    When I log in with the same credentials on another device
    Then both sessions should remain active
    And I should be able to use the system on both devices
    And logout from one device should not affect the other

  @p2
  Scenario: Login page accessibility
    Given I am on the login page
    Then the page should be accessible via keyboard navigation
    And form fields should have proper labels
    And error messages should be announced to screen readers
    And the page should meet WCAG 2.1 AA standards
