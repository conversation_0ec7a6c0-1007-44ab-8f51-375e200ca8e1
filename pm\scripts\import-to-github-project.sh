#!/bin/bash

# CONFIGURATION
PROJECT_ID="PVT_kwDODRQRUM4A9LK-"  # ChMS Roadmap project in JerryAgenyiInc org
CSV_FILE="github-projects-import.csv"

# Confirm project ID
if [ -z "$PROJECT_ID" ]; then
  echo "Project node ID is not set. Please set PROJECT_ID at the top of this script."
  exit 1
fi

echo "Using Project node ID: $PROJECT_ID"

tail -n +2 "$CSV_FILE" | while IFS=, read -r Title Status Milestone Labels Notes; do
  # Remove quotes if present
  Title=$(echo $Title | sed 's/^"//;s/"$//')
  Notes=$(echo $Notes | sed 's/^"//;s/"$//')
  # Only add items that are not already marked as Done
  if [[ "$Status" != "Done" ]]; then
    echo "Adding: $Title"
    gh api graphql -f query="
      mutation {
        addProjectV2DraftIssue(input: {
          projectId: \"$PROJECT_ID\"
          title: \"$Title\"
          body: \"$Notes\"
        }) {
          projectItem {
            id
          }
        }
      }"
    sleep 1 # To avoid hitting rate limits
  fi
done

echo "All items added to GitHub Project!"