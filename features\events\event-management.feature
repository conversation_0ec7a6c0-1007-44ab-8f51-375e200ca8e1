@mvp @p1 @events @organization
Feature: Event Management and Scheduling
  As a Church Administrator or Ministry Leader
  I want to create, manage, and track church events
  So that I can organize effective ministry activities and keep the congregation informed

  Background:
    Given I am logged in as a Church Administrator or Ministry Leader
    And I have permissions to manage events
    And the event management system is available
    And my organization is properly configured

  @p1 @smoke
  Scenario: Create a new church event
    Given I want to organize a church retreat
    When I navigate to "Events" > "Create New Event"
    And I enter event details:
      | Field            | Value                           |
      | Event Name       | Annual Church Retreat 2024     |
      | Event Type       | Retreat                         |
      | Date             | March 15-17, 2024              |
      | Time             | Friday 6 PM - Sunday 4 PM       |
      | Location         | Mountain View Conference Center |
      | Capacity         | 150 people                      |
      | Registration Fee | $75 per person                  |
      | Description      | Three-day spiritual retreat     |
      | Ministry         | Adult Ministry                  |
    And I set registration options:
      | Option              | Setting |
      | Registration Open   | Yes     |
      | Early Bird Discount | $10 off |
      | Family Discount     | 15% off |
      | Deadline           | March 1  |
    And I click "Create Event"
    Then the event should be created successfully
    And I should see "Annual Church Retreat 2024 created"
    And the event should appear in the church calendar

  @p1
  Scenario: Event registration management
    Given the "Annual Church Retreat 2024" event exists
    And registration is open
    When members register for the event:
      | Member Name    | Registration Type | Payment Status |
      | <PERSON>     | Individual        | Paid          |
      | <PERSON>   | Family (3 people) | Pending       |
      | <PERSON>     | Individual        | Paid          |
    Then I should see registration tracking:
      | Metric              | Value |
      | Total Registrations | 5     |
      | Paid Registrations  | 3     |
      | Pending Payments    | 2     |
      | Available Spots     | 145   |
    And I should be able to manage individual registrations
    And I should receive notifications for new registrations

  @p1
  Scenario: Event communication and announcements
    Given I have created the church retreat event
    When I want to promote the event to members
    And I navigate to "Event Communications"
    And I create an announcement:
      | Field           | Value                                    |
      | Title           | Don't Miss Our Annual Retreat!          |
      | Message         | Join us for spiritual renewal...         |
      | Target Audience | All adult members                        |
      | Send Via        | Email, SMS, Church App                   |
      | Schedule        | Send immediately                         |
    And I click "Send Announcement"
    Then the announcement should be sent to all adult members
    And I should see delivery confirmation
    And the announcement should be posted on the church app

  @p1
  Scenario: Recurring event setup
    Given I want to set up weekly Bible study sessions
    When I create a new event with recurrence:
      | Field           | Value                    |
      | Event Name      | Wednesday Bible Study    |
      | Event Type      | Bible Study              |
      | Start Date      | January 10, 2024         |
      | Time            | 7:00 PM - 8:30 PM        |
      | Location        | Church Main Hall         |
      | Recurrence      | Weekly                   |
      | End Recurrence  | December 31, 2024        |
      | Capacity        | 50 people                |
    And I click "Create Recurring Event"
    Then 52 Bible study sessions should be created
    And each session should appear in the calendar
    And members should be able to register for individual sessions

  @p1
  Scenario: Event resource and room booking
    Given I am planning a youth conference
    When I create the event and need to book resources:
      | Resource Type    | Resource Name        | Date/Time Needed      |
      | Room            | Main Auditorium      | Saturday 9 AM - 5 PM  |
      | Equipment       | Sound System         | Saturday 8 AM - 6 PM  |
      | Equipment       | Projector & Screen   | Saturday 9 AM - 5 PM  |
      | Catering        | Lunch for 80 people  | Saturday 12 PM        |
    And I check resource availability
    Then I should see availability status for each resource
    And I should be able to book available resources
    And conflicts should be highlighted with alternatives suggested

  @p2
  Scenario: Event volunteer coordination
    Given I have created a community outreach event
    When I need volunteers for different roles:
      | Role               | People Needed | Skills Required    |
      | Event Coordinator  | 1            | Leadership         |
      | Registration Team  | 3            | Customer service   |
      | Setup Crew        | 5            | Physical work      |
      | Food Service      | 4            | Food handling      |
      | Cleanup Team      | 6            | General help       |
    And I create volunteer opportunities
    Then members should be able to sign up for volunteer roles
    And I should be able to track volunteer commitments
    And I should be able to communicate with volunteers

  @p1
  Scenario: Event attendance tracking
    Given the "Wednesday Bible Study" event is happening
    When I want to track attendance for the session
    And I navigate to "Event Attendance"
    And I record attendance:
      | Member Name     | Status    | Notes              |
      | Alice Brown     | Present   | Participated well  |
      | David Lee       | Present   | Asked good questions|
      | Sarah Wilson    | Absent    | Sick               |
      | Mike Johnson    | Late      | Arrived 7:15 PM    |
    And I save the attendance record
    Then attendance should be recorded for the event
    And attendance statistics should be updated
    And absent members should be flagged for follow-up

  @p1
  Scenario: Event feedback and evaluation
    Given the "Annual Church Retreat 2024" has concluded
    When I want to collect feedback from attendees
    And I create a feedback form:
      | Question Type | Question                                    |
      | Rating        | How would you rate the overall retreat?     |
      | Multiple Choice| Which session was most valuable?           |
      | Text          | What could we improve for next year?        |
      | Rating        | How likely are you to recommend this retreat?|
    And I send the feedback form to all attendees
    Then attendees should receive the feedback form
    And I should be able to collect and analyze responses
    And I should see summary statistics and insights

  @p2
  Scenario: Event budget and financial tracking
    Given I am managing the church retreat budget
    When I set up financial tracking:
      | Category        | Budgeted Amount | Actual Amount | Status    |
      | Venue Rental    | $2,000         | $1,950        | Complete  |
      | Catering        | $3,500         | $3,200        | Complete  |
      | Speaker Fees    | $1,000         | $1,000        | Complete  |
      | Materials       | $500           | $450          | Complete  |
      | Transportation  | $800           | $750          | Complete  |
    And I track registration income:
      | Income Source   | Expected | Actual  |
      | Registration Fees| $11,250 | $10,875 |
      | Donations       | $500     | $650    |
    Then I should see financial summary:
      | Metric          | Amount  |
      | Total Budget    | $8,300  |
      | Total Expenses  | $7,350  |
      | Total Income    | $11,525 |
      | Net Profit      | $4,175  |

  @p2
  Scenario: Multi-location event coordination
    Given our church has multiple locations
    And I want to coordinate a simultaneous event
    When I create a "Multi-Site Prayer Night":
      | Location          | Local Coordinator | Expected Attendance |
      | Main Campus       | Pastor John       | 200                |
      | North Campus      | Pastor Sarah      | 150                |
      | South Campus      | Pastor Mike       | 100                |
    And I set up synchronized activities:
      | Time    | Activity                    | All Locations |
      | 7:00 PM | Welcome and Opening         | Yes          |
      | 7:15 PM | Worship (local choice)      | No           |
      | 7:45 PM | Synchronized Prayer Time    | Yes          |
      | 8:15 PM | Local Ministry Focus        | No           |
      | 8:45 PM | Closing Prayer              | Yes          |
    Then each location should have their customized schedule
    And coordinators should receive location-specific information
    And I should be able to track attendance across all locations
