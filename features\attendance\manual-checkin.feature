@mvp @p0 @attendance @offline
Feature: Manual Check-in System
  As a Church Staff Member
  I want to manually check in members for services and events
  So that I can track attendance even when QR codes or digital methods aren't available

  Background:
    Given I am logged in as a Church Staff Member
    And I am on the Manual Check-in page
    And church services are scheduled
    And members exist in the system

  @smoke @p0
  Scenario: Manual check-in for a single member
    Given today's service "Sunday Morning Worship" is available for check-in
    And member "<PERSON> Doe" is registered in the system
    When I select the service "Sunday Morning Worship"
    And I search for member "<PERSON>"
    And I select "John Doe" from the search results
    And I click "Check In"
    Then <PERSON> should be marked as present for the service
    And I should see "<PERSON> checked in successfully"
    And the attendance record should be created with current timestamp
    And the check-in should be attributed to my user account

  @p0
  Scenario: Manual check-in for family members
    Given today's service "Sunday Morning Worship" is available
    And the "Smith Family" has multiple members:
      | Name        | Age | Relationship |
      | <PERSON>   | 45  | Father       |
      | <PERSON>  | 42  | Mother       |
      | <PERSON>   | 16  | Son          |
      | <PERSON>  | 14  | Daughter     |
    When I select the service "Sunday Morning Worship"
    And I search for "Smith Family"
    And I select all family members for check-in
    And I click "Check In Family"
    Then all Smith family members should be marked as present
    And I should see "4 family members checked in successfully"
    And individual attendance records should be created for each member

  @p0
  Scenario: Service selection and availability
    Given multiple services are scheduled for today:
      | Service Name           | Time  | Status    |
      | Sunday Morning Worship | 09:00 | Active    |
      | Sunday School          | 10:30 | Active    |
      | Evening Service        | 18:00 | Scheduled |
    When I am on the Manual Check-in page
    Then I should see all available services in the dropdown
    And I should be able to select "Sunday Morning Worship"
    And I should be able to select "Sunday School"
    And "Evening Service" should show as "Not yet started"

  @p0
  Scenario: Member search functionality
    Given I have selected a service for check-in
    When I type "John" in the member search field
    Then I should see all members with "John" in their name
    And the results should show:
      | Name       | Phone       | Last Attendance |
      | John Doe   | 555-0123    | Last Sunday     |
      | John Smith | 555-0456    | 2 weeks ago     |
    And I should be able to select any member from the results

  @p0
  Scenario: Check-in validation and duplicate prevention
    Given member "John Doe" is already checked in for "Sunday Morning Worship"
    When I search for "John Doe"
    And I attempt to check him in again
    Then I should see "John Doe is already checked in for this service"
    And the duplicate check-in should be prevented
    And I should see the original check-in time

  @p1 @offline
  Scenario: Offline manual check-in capability
    Given I am using the system offline
    And I have cached member data available
    When I select a service for check-in
    And I search for and select members
    And I perform check-ins
    Then the check-ins should be stored locally
    And I should see "Check-ins saved offline"
    When the connection is restored
    Then the offline check-ins should sync automatically
    And I should see "Offline check-ins synchronized"

  @p1
  Scenario: Bulk check-in for groups
    Given I have selected "Sunday Morning Worship" service
    And I want to check in the "Youth Group" members
    When I click "Group Check-in"
    And I select "Youth Group" from the group list
    And I see all youth group members listed
    And I select all present members
    And I click "Check In Selected"
    Then all selected youth group members should be checked in
    And I should see "12 youth group members checked in"

  @p1
  Scenario: Late arrival check-in
    Given "Sunday Morning Worship" started 30 minutes ago
    And member "Jane Smith" arrives late
    When I search for "Jane Smith"
    And I select her for check-in
    And I click "Check In"
    Then Jane Smith should be checked in successfully
    And the check-in should record the actual arrival time
    And I should see "Jane Smith checked in (late arrival)"

  @p1
  Scenario: Check-in with additional information
    Given I am checking in member "Bob Wilson"
    When I select "Bob Wilson" for check-in
    And I add a note "First time visitor, needs welcome packet"
    And I mark him as "First Time Visitor"
    And I click "Check In"
    Then Bob Wilson should be checked in with the additional information
    And the note should be saved with the attendance record
    And he should be flagged for follow-up

  @p2
  Scenario: Check-in statistics and summary
    Given I have completed multiple check-ins for the service
    When I view the check-in summary
    Then I should see:
      | Metric              | Value |
      | Total Checked In    | 45    |
      | Adults              | 28    |
      | Children            | 17    |
      | First Time Visitors | 3     |
      | Late Arrivals       | 5     |
    And I should be able to export the attendance list

  @p2
  Scenario: Undo check-in functionality
    Given member "Tom Brown" was checked in by mistake
    When I search for "Tom Brown" in today's attendance
    And I click "Undo Check-in" next to his name
    And I confirm the action
    Then Tom Brown's check-in should be removed
    And I should see "Check-in removed for Tom Brown"
    And the action should be logged for audit purposes
