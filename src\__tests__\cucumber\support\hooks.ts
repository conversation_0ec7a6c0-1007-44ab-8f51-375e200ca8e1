import { Before, After, BeforeAll, AfterAll, Status, setDefaultTimeout } from '@cucumber/cucumber';
import { CustomWorld } from './world';
import { vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import fs from 'fs';
import path from 'path';

// Set default timeout for all steps (30 seconds)
setDefaultTimeout(30 * 1000);

// Ensure reports directory exists
BeforeAll(async function() {
  const reportsDir = path.join(process.cwd(), 'reports', 'cucumber');
  const screenshotsDir = path.join(reportsDir, 'screenshots');
  
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }
});

// Before each scenario
Before(async function(this: CustomWorld, scenario) {
  // Reset all mocks
  vi.clearAllMocks();
  
  // Clear any previous test data
  this.testData = {};
  this.currentUser = undefined;
  this.apiResponses = {};
  
  // Set up browser for scenarios that need it
  const needsBrowser = scenario.pickle.tags.some(tag => 
    ['@browser', '@e2e', '@ui'].includes(tag.name)
  );
  
  if (needsBrowser) {
    await this.setupBrowser();
  }
  
  // Set up specific test environment based on tags
  if (scenario.pickle.tags.some(tag => tag.name === '@africa')) {
    // Configure for Africa-specific testing
    this.testData.networkCondition = 'slow';
    this.testData.bandwidth = 'low';
  }
  
  if (scenario.pickle.tags.some(tag => tag.name === '@ai')) {
    // Mock AI services for AI integration tests
    this.mockApiResponse('/api/ai/claude', { 
      message: 'Welcome to our church family!' 
    });
  }
  
  if (scenario.pickle.tags.some(tag => tag.name === '@offline')) {
    // Configure offline testing
    if (this.page) {
      await this.page.setOfflineMode(true);
    }
  }
});

// After each scenario
After(async function(this: CustomWorld, scenario) {
  // Take screenshot on failure
  if (scenario.result?.status === Status.FAILED && this.page) {
    const scenarioName = scenario.pickle.name.replace(/[^a-zA-Z0-9]/g, '_');
    await this.takeScreenshot(`failed_${scenarioName}_${Date.now()}`);
  }
  
  // Clean up React Testing Library
  cleanup();
  
  // Clean up browser
  await this.cleanup();
});

// Before scenarios with specific tags
Before('@database', async function(this: CustomWorld) {
  // Set up database with test data
  await this.seedDatabase({
    users: [
      {
        id: '1',
        email: '<EMAIL>',
        name: 'Church Administrator',
        role: 'ADMIN'
      },
      {
        id: '2',
        email: '<EMAIL>',
        name: 'Pastor John',
        role: 'PASTOR'
      }
    ],
    members: [
      {
        id: '1',
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+1234567890'
      }
    ]
  });
});

Before('@authentication', async function(this: CustomWorld) {
  // Set up authentication test environment
  this.testData.authConfig = {
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    sessionTimeout: 30 * 60 * 1000   // 30 minutes
  };
});

Before('@member-management', async function(this: CustomWorld) {
  // Set up member management test data
  await this.seedDatabase({
    members: [
      {
        id: '1',
        firstName: 'John',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+1234567890',
        status: 'ACTIVE'
      },
      {
        id: '2',
        firstName: 'Mary',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '+1234567891',
        status: 'ACTIVE'
      }
    ],
    families: [
      {
        id: '1',
        name: 'Smith Family',
        headOfHousehold: '1'
      }
    ]
  });
});

Before('@attendance', async function(this: CustomWorld) {
  // Set up attendance test data
  await this.seedDatabase({
    services: [
      {
        id: '1',
        name: 'Sunday Morning Service',
        date: new Date().toISOString(),
        startTime: '09:00',
        endTime: '11:00'
      }
    ],
    attendance: []
  });
});

Before('@events', async function(this: CustomWorld) {
  // Set up event management test data
  await this.seedDatabase({
    events: [
      {
        id: '1',
        name: 'Annual Church Retreat',
        date: '2024-03-15',
        location: 'Mountain View Conference Center',
        capacity: 150
      }
    ]
  });
});

Before('@financial', async function(this: CustomWorld) {
  // Set up financial test data
  await this.seedDatabase({
    givingRecords: [
      {
        id: '1',
        memberId: '1',
        amount: 15000,
        type: 'TITHE',
        date: new Date().toISOString()
      }
    ]
  });
});

// Clean up after all scenarios
AfterAll(async function() {
  // Perform any global cleanup
  console.log('Cucumber tests completed');
});
