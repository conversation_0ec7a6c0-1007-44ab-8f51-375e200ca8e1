# Cursor Rules

This directory contains rule files used by the Cursor editor to enforce project standards and provide guidance during development.

## Rule Files

- **chakra-animations.mdc**: Standards for using animations with Chakra UI
- **chakra-components.mdc**: Guidelines for Chakra UI component usage
- **chakra-forms.mdc**: Standards for form implementation with Chakra UI
- **chakra-usage.mdc**: General Chakra UI usage guidelines
- **language-standards.mdc**: Language standards for code and documentation
- **technical-specifications.mdc**: Technical specifications and requirements
- **testing.mdc**: Testing standards and practices

## Related Documentation

These rules complement the project's main documentation:

- **Development Standards**: [docs/standards/development-standards.md](../../docs/standards/development-standards.md)
- **Testing Standards**: [docs/standards/testing-standards.md](../../docs/standards/testing-standards.md)
- **Documentation Standards**: [docs/standards/documentation-standards.md](../../docs/standards/documentation-standards.md)
- **Technical Architecture**: [pm/technical-architecture.md](../../pm/technical-architecture.md)

## Purpose

These rules are used by the Cursor editor to:

1. Provide inline guidance during development
2. Enforce project standards
3. Ensure consistency across the codebase
4. Assist new developers in following project conventions

For a complete overview of the project documentation, please see [DOCUMENTATION.md](../../DOCUMENTATION.md).

## Version History

### 1.0.0 - [Current Date]
- Initial creation of the rules README
