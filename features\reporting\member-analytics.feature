@mvp @p1 @reporting @analytics @member-management
Feature: Member Analytics and Growth Reports
  As a Church Administrator or Pastor
  I want to analyze member data and growth patterns
  So that I can understand church health, plan ministry strategies, and track evangelism effectiveness

  Background:
    Given I am logged in as a Church Administrator or Pastor
    And I have permissions to view member analytics
    And member data exists in the system
    And the church has been operating for multiple months

  @p1 @smoke
  Scenario: Member growth overview dashboard
    Given I want to see overall member growth trends
    When I navigate to "Analytics" > "Member Growth"
    And I select the time period "Last 12 months"
    Then I should see a growth overview dashboard showing:
      | Metric                  | Current | Previous Period | Change |
      | Total Members          | 245     | 220            | +11.4% |
      | New Members This Month | 8       | 6              | +33.3% |
      | Active Members         | 198     | 185            | +7.0%  |
      | Inactive Members       | 47      | 35             | +34.3% |
    And I should see visual charts showing growth trends over time
    And I should see key insights and recommendations

  @p1
  Scenario: New member acquisition analysis
    Given I want to analyze how new members are joining
    When I generate a "New Member Analysis" report
    And I set parameters:
      | Parameter      | Value           |
      | Date Range     | Last 6 months   |
      | Include Source | Yes             |
      | Group By       | Month           |
    Then I should see new member acquisition data:
      | Month     | New Members | Source: Referral | Source: Event | Source: Online |
      | October   | 12         | 7               | 3            | 2             |
      | September | 8          | 5               | 2            | 1             |
      | August    | 15         | 9               | 4            | 2             |
    And I should see the most effective acquisition channels
    And I should see conversion rates by source

  @p1
  Scenario: Member engagement scoring and analysis
    Given I want to measure member engagement levels
    When I navigate to "Member Engagement Analytics"
    And I configure engagement metrics:
      | Metric              | Weight | Criteria                    |
      | Attendance Rate     | 40%    | Regular service attendance  |
      | Ministry Involvement| 30%    | Active in ministries        |
      | Event Participation | 20%    | Attends church events       |
      | Communication      | 10%    | Responds to church comms    |
    And I generate the engagement report
    Then I should see member engagement scores:
      | Engagement Level | Member Count | Percentage |
      | Highly Engaged   | 78          | 32%        |
      | Moderately Engaged| 95         | 39%        |
      | Low Engagement   | 52          | 21%        |
      | At Risk          | 20          | 8%         |
    And I should see specific members needing attention

  @p1
  Scenario: Member demographics analysis
    Given I want to understand our member demographics
    When I generate a "Demographics Report"
    And I include demographic breakdowns:
      | Category        | Include |
      | Age Groups      | Yes     |
      | Gender         | Yes     |
      | Family Status  | Yes     |
      | Location       | Yes     |
      | Join Date      | Yes     |
    Then I should see demographic distribution:
      | Age Group | Count | Percentage |
      | 0-17      | 45    | 18%        |
      | 18-35     | 78    | 32%        |
      | 36-55     | 85    | 35%        |
      | 56+       | 37    | 15%        |
    And I should see insights for ministry planning
    And I should see areas for targeted outreach

  @p2
  Scenario: Member retention analysis
    Given I want to analyze member retention patterns
    When I navigate to "Member Retention Analytics"
    And I set analysis parameters:
      | Parameter          | Value              |
      | Cohort Period      | Monthly            |
      | Analysis Period    | Last 2 years       |
      | Retention Metrics  | 3, 6, 12 months    |
    And I generate the retention analysis
    Then I should see cohort retention data:
      | Join Month | Initial Count | 3-Month Retention | 6-Month Retention | 12-Month Retention |
      | Jan 2023   | 15           | 87%              | 73%              | 60%               |
      | Feb 2023   | 12           | 92%              | 83%              | 67%               |
      | Mar 2023   | 18           | 78%              | 67%              | 56%               |
    And I should see factors affecting retention
    And I should see recommendations for improvement

  @p1
  Scenario: Member lifecycle stage analysis
    Given I want to understand member journey stages
    When I generate a "Member Lifecycle Report"
    And I define lifecycle stages:
      | Stage        | Criteria                           | Duration    |
      | New Visitor  | First 2 visits                     | 0-1 month   |
      | Regular Visitor| 3+ visits, not yet member        | 1-6 months  |
      | New Member   | Recently joined                    | 0-6 months  |
      | Growing Member| Active participation               | 6-24 months |
      | Mature Member| Leadership/ministry involvement    | 2+ years    |
    Then I should see lifecycle distribution:
      | Stage          | Count | Percentage | Avg. Time in Stage |
      | New Visitor    | 25    | 10%        | 2.3 months        |
      | Regular Visitor| 18    | 7%         | 4.1 months        |
      | New Member     | 35    | 14%        | 8.2 months        |
      | Growing Member | 89    | 36%        | 18.5 months       |
      | Mature Member  | 78    | 32%        | 3.2 years         |
    And I should see progression rates between stages

  @p2
  Scenario: Member ministry involvement analysis
    Given I want to analyze member participation in ministries
    When I navigate to "Ministry Involvement Analytics"
    And I generate the involvement report
    Then I should see ministry participation data:
      | Ministry        | Total Members | Active Members | Participation Rate |
      | Worship Team    | 25           | 22            | 88%               |
      | Youth Ministry  | 45           | 38            | 84%               |
      | Children's Min  | 30           | 25            | 83%               |
      | Outreach       | 20           | 14            | 70%               |
      | Administration | 15           | 12            | 80%               |
    And I should see member overlap between ministries
    And I should identify members not involved in any ministry

  @p1
  Scenario: Member communication preferences analysis
    Given I want to optimize communication with members
    When I generate a "Communication Analytics" report
    Then I should see communication preference data:
      | Communication Method | Preferred | Response Rate | Effectiveness |
      | Email               | 65%       | 45%          | High         |
      | SMS                 | 78%       | 62%          | Very High    |
      | WhatsApp            | 85%       | 71%          | Very High    |
      | Phone Call          | 35%       | 89%          | High         |
      | Physical Mail       | 15%       | 23%          | Low          |
    And I should see optimal communication timing
    And I should see member segments by communication preference

  @p2
  Scenario: Predictive member analytics
    Given I have sufficient historical member data
    When I navigate to "Predictive Analytics"
    And I run predictive models for:
      | Model Type           | Purpose                        |
      | Churn Prediction     | Identify members at risk       |
      | Engagement Forecast  | Predict future engagement      |
      | Growth Projection    | Forecast membership growth     |
    Then I should see predictive insights:
      | Prediction Type      | Result                         |
      | Members at Risk      | 12 members (5% probability)    |
      | Expected New Members | 8-12 next month               |
      | Engagement Trend     | Slight decline expected        |
    And I should see recommended actions for each prediction

  @p1
  Scenario: Member analytics export and reporting
    Given I have generated member analytics
    When I want to share insights with church leadership
    And I select "Export Analytics"
    And I choose export options:
      | Option          | Selection                    |
      | Format          | Executive Summary PDF        |
      | Include Charts  | Yes                         |
      | Detail Level    | High-level insights         |
      | Recipients      | Pastor, Board, Ministry Heads|
    And I click "Generate Executive Report"
    Then a comprehensive analytics report should be created
    And it should include key insights and recommendations
    And it should be formatted for leadership presentation
