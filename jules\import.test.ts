import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createMocks, RequestMethod } from 'node-mocks-http';
import type { NextApiRequest, NextApiResponse } from 'next';
import importMembersHandler from '@/pages/api/members/import';
import { prismaMock } from '@/tests/mocks/prisma'; // Assuming global prisma mock setup
import * as nextAuth from 'next-auth';
import formidable from 'formidable';
import fs from 'fs';
import stream from 'stream';

// Mock next-auth
vi.mock('next-auth', async (importOriginal) => {
  const actual = await importOriginal<typeof nextAuth>();
  return {
    ...actual,
    getServerSession: vi.fn(),
  };
});

// Mock formidable
const mockParse = vi.fn();
vi.mock('formidable', () => {
  const actualFormidable = vi.importActual('formidable') as any;
  return {
    ...actualFormidable,
    IncomingForm: vi.fn(() => ({
      parse: mockParse,
    })),
    // Keep other exports like 'File' if needed, though direct instantiation might not be necessary here
  };
});


// Mock fs
vi.mock('fs', async (importOriginal) => {
  const actualFs = await importOriginal<typeof fs>();
  return {
    ...actualFs,
    createReadStream: vi.fn(),
    unlink: vi.fn((path, callback) => callback(null)), // Mock unlink to succeed by default
  };
});


describe('POST /api/members/import', () => {
  const mockSession = {
    user: {
      email: '<EMAIL>',
      name: 'Admin User',
      organizationId: 'org123',
    },
    expires: '1',
  };

  beforeEach(() => {
    vi.resetAllMocks();
    (nextAuth.getServerSession as vi.Mock).mockResolvedValue(mockSession);
  });

  const executeHandler = (reqOptions: {
    method?: RequestMethod;
    body?: any;
    headers?: any;
    file?: { filepath: string; mimetype: string; originalFilename?: string };
    parseError?: Error;
    csvData?: string;
  }) => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: reqOptions.method || 'POST',
      headers: reqOptions.headers,
      // Writable stream for formidable to write to if needed, but we mock parse
    });

    // Setup mock for formidable.parse
    mockParse.mockImplementation((request, callback) => {
      if (reqOptions.parseError) {
        callback(reqOptions.parseError, {}, {});
        return;
      }
      const files = reqOptions.file ? { file: reqOptions.file } : {};
      callback(null, {}, files); // No fields, just files
    });
    
    // Setup mock for fs.createReadStream
    if (reqOptions.csvData && reqOptions.file) {
        const readableStream = new stream.Readable();
        readableStream._read = () => {}; // _read is required
        readableStream.push(reqOptions.csvData);
        readableStream.push(null); // No more data
        (fs.createReadStream as vi.Mock).mockReturnValue(readableStream);
    } else if (reqOptions.file) {
        // Default empty stream if no csvData provided but file is expected
        const readableStream = new stream.Readable();
        readableStream._read = () => {};
        readableStream.push(null);
        (fs.createReadStream as vi.Mock).mockReturnValue(readableStream);
    }


    return { req, res, handlerPromise: importMembersHandler(req, res) };
  };

  it('should return 401 if user is not authenticated', async () => {
    (nextAuth.getServerSession as vi.Mock).mockResolvedValue(null);
    const { res, handlerPromise } = executeHandler({});
    await handlerPromise;
    expect(res._getStatusCode()).toBe(401);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Unauthorized: No session or organizationId' });
  });

  it('should return 405 if method is not POST', async () => {
    const { res, handlerPromise } = executeHandler({ method: 'GET' });
    await handlerPromise;
    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Method GET Not Allowed' });
  });

  it('should return 400 if no file is uploaded', async () => {
    const { res, handlerPromise } = executeHandler({ file: undefined });
    await handlerPromise;
    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: 'No file uploaded' });
  });
  
  it('should return 400 if file is not CSV', async () => {
    const { res, handlerPromise } = executeHandler({ 
        file: { filepath: 'dummy/path/file.txt', mimetype: 'text/plain', originalFilename: 'file.txt' } 
    });
    await handlerPromise;
    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Invalid file type. Only CSV is allowed.' });
  });

  it('should return 400 for CSV with missing headers', async () => {
    const csvData = "email,name\<EMAIL>,John"; // Missing other required headers
    const { res, handlerPromise } = executeHandler({
      file: { filepath: 'dummy/path/members.csv', mimetype: 'text/csv' },
      csvData,
    });
    await handlerPromise;
    expect(res._getStatusCode()).toBe(400);
    const responseBody = JSON.parse(res._getData());
    expect(responseBody.errors).toContain('CSV Header mismatch. Missing: role, department, phoneNumber, dateOfBirth, address');
  });
  
  it('should process a valid CSV and import members', async () => {
    const csvData = `email,name,role,department,phoneNumber,dateOfBirth,address
<EMAIL>,Test User,MEMBER,Test Dept,1234567890,1990-01-01,123 Test St
<EMAIL>,Another User,MEMBER,Test Dept,9876543210,1992-05-10,456 Test Ave`;
    
    prismaMock.member.create.mockResolvedValueOnce({ id: 'mem1'} as any)
                             .mockResolvedValueOnce({ id: 'mem2'} as any);

    const { res, handlerPromise } = executeHandler({
      file: { filepath: 'dummy/path/members.csv', mimetype: 'text/csv' },
      csvData,
    });
    await handlerPromise;

    expect(res._getStatusCode()).toBe(200);
    const responseBody = JSON.parse(res._getData());
    expect(responseBody.successful).toBe(2);
    expect(responseBody.failed).toBe(0);
    expect(responseBody.errors.length).toBe(0);
    expect(prismaMock.member.create).toHaveBeenCalledTimes(2);
    expect(prismaMock.member.create).toHaveBeenCalledWith({
      data: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        phone: '1234567890',
        organizationId: 'org123',
        dateOfBirth: new Date('1990-01-01T00:00:00.000Z'),
      },
    });
  });

  it('should handle rows with validation errors and some successful imports', async () => {
    const csvData = `email,name,role,department,phoneNumber,dateOfBirth,address
<EMAIL>,Valid User,MEMBER,Dept,1112223333,1985-03-15,Valid Address
invalidemail,Invalid User,MEMBER,Dept,2223334444,1990-01-01, Address
<EMAIL>,Valid User Two,,1234567890,1990-01-01,123 Test St,1995-07-20,Another Address`; // Name missing for 3rd valid entry
    
    prismaMock.member.create.mockResolvedValueOnce({ id: 'memValid1' } as any)
                             .mockResolvedValueOnce({ id: 'memValid2' } as any); // only two should succeed

    const { res, handlerPromise } = executeHandler({
      file: { filepath: 'dummy/path/members.csv', mimetype: 'text/csv' },
      csvData,
    });
    await handlerPromise;
    
    expect(res._getStatusCode()).toBe(200);
    const responseBody = JSON.parse(res._getData());
    expect(responseBody.successful).toBe(2); // Assuming 'Valid User Two' is fine despite missing department
    expect(responseBody.failed).toBe(1);
    expect(responseBody.errors.length).toBe(1);
    expect(responseBody.errors[0]).toContain('Row 3 (invalidemail): email - Invalid email format');
    expect(prismaMock.member.create).toHaveBeenCalledTimes(2);
  });
  
  it('should handle database errors during member creation', async () => {
    const csvData = `email,name,role,department,phoneNumber,dateOfBirth,address
<EMAIL>,User One,MEMBER,Dept,123,1990-01-01,Addr1
<EMAIL>,User Two,MEMBER,Dept,456,1991-02-02,Addr2`;

    prismaMock.member.create.mockResolvedValueOnce({ id: 'mem1' } as any);
    prismaMock.member.create.mockRejectedValueOnce(new Error('Test DB Error')); // Second create fails

    const { res, handlerPromise } = executeHandler({
      file: { filepath: 'dummy/path/members.csv', mimetype: 'text/csv' },
      csvData,
    });
    await handlerPromise;

    expect(res._getStatusCode()).toBe(200);
    const responseBody = JSON.parse(res._getData());
    expect(responseBody.successful).toBe(1);
    expect(responseBody.failed).toBe(1);
    expect(responseBody.errors[0]).toBe('Row (Email: <EMAIL>): Database error.');
  });

  it('should correctly parse names into firstName and lastName', async () => {
    const csvData = `email,name,role,department,phoneNumber,dateOfBirth,address
<EMAIL>,SingleName,MEMBER,Dept,123,1990-01-01,Addr
<EMAIL>,Multi Part Name,MEMBER,Dept,456,1991-02-02,Addr`;
    
    prismaMock.member.create.mockResolvedValue({} as any); // Mock successful creation

    const { handlerPromise } = executeHandler({
        file: { filepath: 'dummy/path/members.csv', mimetype: 'text/csv' },
        csvData,
    });
    await handlerPromise;

    expect(prismaMock.member.create).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({ email: '<EMAIL>', firstName: 'SingleName', lastName: undefined })
    }));
    expect(prismaMock.member.create).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({ email: '<EMAIL>', firstName: 'Multi', lastName: 'Part Name' })
    }));
  });

  afterEach(() => {
    // Ensure mocks are cleared if they were set up to persist or have side effects
    vi.clearAllMocks();
  });
});

[end of src/__tests__/pages/api/members/import.test.ts]
