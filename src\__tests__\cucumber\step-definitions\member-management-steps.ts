import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { CustomWorld } from '../support/world';

// Member profile management
Given('I am on the member management page', async function(this: CustomWorld) {
  await this.navigateTo('/members');
});

Given('I am viewing a member profile for {string}', async function(this: CustomWorld, memberName: string) {
  // Mock member data
  const [firstName, lastName] = memberName.split(' ');
  this.testData.currentMember = {
    id: '1',
    firstName,
    lastName,
    email: `${firstName.toLowerCase()}@example.com`,
    phone: '+1234567890'
  };
  
  await this.navigateTo('/members/1');
});

When('I click {string}', async function(this: CustomWorld, buttonText: string) {
  const selector = `button:has-text("${buttonText}"), [data-testid="${buttonText.toLowerCase().replace(/\s+/g, '-')}"]`;
  await this.page?.click(selector);
});

When('I update the member information:', async function(this: CustomWorld, dataTable) {
  const memberData = dataTable.rowsHash();
  await this.fillForm(memberData);
});

When('I save the changes', async function(this: CustomWorld) {
  await this.page?.click('[data-testid="save-button"], button[type="submit"]');
});

Then('the member profile should be updated successfully', async function(this: CustomWorld) {
  await this.page?.waitForSelector('[data-testid="success-message"]');
  const successMessage = await this.page?.textContent('[data-testid="success-message"]');
  expect(successMessage).toContain('Member updated successfully');
});

Then('I should see the updated information displayed', async function(this: CustomWorld) {
  // Verify the updated information is displayed
  const memberName = await this.page?.textContent('[data-testid="member-name"]');
  expect(memberName).toBeDefined();
});

// Member search and filtering
When('I search for {string}', async function(this: CustomWorld, searchTerm: string) {
  await this.page?.fill('[data-testid="search-input"]', searchTerm);
  await this.page?.press('[data-testid="search-input"]', 'Enter');
});

When('I filter by {string}', async function(this: CustomWorld, filterOption: string) {
  await this.page?.selectOption('[data-testid="filter-select"]', filterOption);
});

Then('I should see {int} member(s) in the results', async function(this: CustomWorld, expectedCount: number) {
  await this.page?.waitForSelector('[data-testid="member-list"]');
  const memberRows = await this.page?.$$('[data-testid="member-row"]');
  expect(memberRows?.length).toBe(expectedCount);
});

Then('the results should include {string}', async function(this: CustomWorld, memberName: string) {
  const memberList = await this.page?.textContent('[data-testid="member-list"]');
  expect(memberList).toContain(memberName);
});

// CSV Import scenarios
Given('I have a CSV file with member data', async function(this: CustomWorld) {
  this.testData.csvFile = 'members-import.csv';
  // In a real test, this would reference an actual test file
});

Given('the CSV file contains {int} member records', async function(this: CustomWorld, recordCount: number) {
  this.testData.csvRecordCount = recordCount;
});

When('I navigate to the member import page', async function(this: CustomWorld) {
  await this.navigateTo('/members/import');
});

When('I upload the CSV file', async function(this: CustomWorld) {
  const filePath = `src/__tests__/fixtures/${this.testData.csvFile}`;
  await this.page?.setInputFiles('[data-testid="file-upload"]', filePath);
});

When('I click {string}', async function(this: CustomWorld, buttonText: string) {
  const selector = `button:has-text("${buttonText}"), [data-testid="${buttonText.toLowerCase().replace(/\s+/g, '-')}"]`;
  await this.page?.click(selector);
});

Then('I should see a preview of the import data', async function(this: CustomWorld) {
  await this.page?.waitForSelector('[data-testid="import-preview"]');
  const preview = await this.page?.locator('[data-testid="import-preview"]');
  await expect(preview).toBeVisible();
});

Then('the preview should show {int} records', async function(this: CustomWorld, expectedCount: number) {
  const previewRows = await this.page?.$$('[data-testid="preview-row"]');
  expect(previewRows?.length).toBe(expectedCount);
});

When('I confirm the import', async function(this: CustomWorld) {
  await this.page?.click('[data-testid="confirm-import"]');
});

Then('the import should be processed successfully', async function(this: CustomWorld) {
  await this.page?.waitForSelector('[data-testid="import-success"]');
  const successMessage = await this.page?.textContent('[data-testid="import-success"]');
  expect(successMessage).toContain('Import completed successfully');
});

Then('{int} new members should be added to the system', async function(this: CustomWorld, expectedCount: number) {
  const importSummary = await this.page?.textContent('[data-testid="import-summary"]');
  expect(importSummary).toContain(`${expectedCount} members imported`);
});

// AI-powered welcome message scenarios
Given('AI integration is enabled', async function(this: CustomWorld) {
  this.testData.aiEnabled = true;
  // Mock AI service response
  this.mockApiResponse('/api/ai/claude', {
    message: 'Welcome to our church family! We are excited to have you join us on this spiritual journey.'
  });
});

When('the import process completes', async function(this: CustomWorld) {
  // Wait for import completion
  await this.page?.waitForSelector('[data-testid="import-complete"]');
});

Then('AI-generated welcome messages should be created', async function(this: CustomWorld) {
  await this.page?.waitForSelector('[data-testid="ai-messages-generated"]');
  const aiMessage = await this.page?.textContent('[data-testid="ai-messages-generated"]');
  expect(aiMessage).toContain('Welcome messages generated');
});

Then('each new member should have a personalized welcome message', async function(this: CustomWorld) {
  // Navigate to a member's profile to verify welcome message
  await this.page?.click('[data-testid="view-member"]:first-child');
  await this.page?.waitForSelector('[data-testid="welcome-message"]');
  const welcomeMessage = await this.page?.textContent('[data-testid="welcome-message"]');
  expect(welcomeMessage).toContain('Welcome to our church family');
});

// Family unit management
Given('I want to create a family unit', async function(this: CustomWorld) {
  await this.navigateTo('/families/new');
});

When('I enter family details:', async function(this: CustomWorld, dataTable) {
  const familyData = dataTable.rowsHash();
  await this.fillForm(familyData);
});

When('I add family members:', async function(this: CustomWorld, dataTable) {
  const members = dataTable.hashes();
  
  for (const member of members) {
    await this.page?.click('[data-testid="add-family-member"]');
    await this.fillForm({
      firstName: member['First Name'],
      lastName: member['Last Name'],
      relationship: member['Relationship'],
      dateOfBirth: member['Date of Birth']
    });
  }
});

When('I save the family unit', async function(this: CustomWorld) {
  await this.page?.click('[data-testid="save-family"]');
});

Then('the family unit should be created successfully', async function(this: CustomWorld) {
  await this.page?.waitForSelector('[data-testid="family-created"]');
  const successMessage = await this.page?.textContent('[data-testid="family-created"]');
  expect(successMessage).toContain('Family created successfully');
});

Then('I should see the family listed with {int} members', async function(this: CustomWorld, memberCount: number) {
  const familyMemberCount = await this.page?.textContent('[data-testid="family-member-count"]');
  expect(familyMemberCount).toContain(`${memberCount} members`);
});

// Member status management
When('I change the member status to {string}', async function(this: CustomWorld, status: string) {
  await this.page?.selectOption('[data-testid="member-status"]', status);
  await this.page?.click('[data-testid="update-status"]');
});

Then('the member status should be updated to {string}', async function(this: CustomWorld, expectedStatus: string) {
  await this.page?.waitForSelector('[data-testid="status-updated"]');
  const currentStatus = await this.page?.textContent('[data-testid="current-status"]');
  expect(currentStatus).toContain(expectedStatus);
});

// Member communication preferences
When('I update communication preferences:', async function(this: CustomWorld, dataTable) {
  const preferences = dataTable.rowsHash();
  
  for (const [method, preference] of Object.entries(preferences)) {
    const checkbox = `[data-testid="comm-${method.toLowerCase()}"]`;
    if (preference === 'Yes') {
      await this.page?.check(checkbox);
    } else {
      await this.page?.uncheck(checkbox);
    }
  }
  
  await this.page?.click('[data-testid="save-preferences"]');
});

Then('the communication preferences should be saved', async function(this: CustomWorld) {
  await this.page?.waitForSelector('[data-testid="preferences-saved"]');
  const message = await this.page?.textContent('[data-testid="preferences-saved"]');
  expect(message).toContain('Preferences updated');
});

// Member directory and privacy
Given('member directory privacy is enabled', async function(this: CustomWorld) {
  this.testData.directoryPrivacy = true;
});

When('I view the member directory', async function(this: CustomWorld) {
  await this.navigateTo('/directory');
});

Then('I should only see members who have opted in to directory listing', async function(this: CustomWorld) {
  const directoryMembers = await this.page?.$$('[data-testid="directory-member"]');
  // Verify that only public members are shown
  expect(directoryMembers?.length).toBeGreaterThan(0);
});

Then('private member information should not be visible', async function(this: CustomWorld) {
  const privateInfo = await this.page?.$$('[data-testid="private-info"]');
  expect(privateInfo?.length).toBe(0);
});
