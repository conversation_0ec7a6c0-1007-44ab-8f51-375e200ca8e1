"use client";

import { useState, useEffect } from "react";
import {
  Box,
  VStack,
  Heading,
  Text,
  Select,
  FormControl,
  FormLabel,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Spinner,
  Center,
} from "@chakra-ui/react";
import CheckInForm from "@/components/attendance/CheckInForm";

interface Member {
  id: string;
  name: string;
  email: string;
}

interface Service {
  id: string;
  name: string;
  date: string;
  location?: string;
}

interface CheckInData {
  memberId?: string;
  memberIds?: string[];
  type: "INDIVIDUAL" | "FAMILY";
  serviceId: string;
}

export default function ManualCheckInPage() {
  const [members, setMembers] = useState<Member[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [selectedServiceId, setSelectedServiceId] = useState<string>("");
  const [isLoadingMembers, setIsLoadingMembers] = useState(true);
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();

  // Fetch members and services on component mount
  useEffect(() => {
    fetchMembers();
    fetchServices();
  }, []);

  const fetchMembers = async () => {
    try {
      setIsLoadingMembers(true);
      const response = await fetch("/api/members");
      if (!response.ok) {
        throw new Error("Failed to fetch members");
      }
      const data = await response.json();
      setMembers(data);
    } catch (error) {
      console.error("Error fetching members:", error);
      setError("Failed to load members. Please refresh the page.");
    } finally {
      setIsLoadingMembers(false);
    }
  };

  const fetchServices = async () => {
    try {
      setIsLoadingServices(true);
      const response = await fetch("/api/services");
      if (!response.ok) {
        throw new Error("Failed to fetch services");
      }
      const data = await response.json();
      setServices(data);
    } catch (error) {
      console.error("Error fetching services:", error);
      setError("Failed to load services. Please refresh the page.");
    } finally {
      setIsLoadingServices(false);
    }
  };

  const handleCheckInSubmit = async (data: CheckInData) => {
    try {
      const response = await fetch("/api/attendance/check-in", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Check-in failed");
      }

      const result = await response.json();
      
      toast({
        title: "Check-in Successful",
        description: data.type === "FAMILY" 
          ? `Successfully checked in ${data.memberIds?.length} family members`
          : "Member checked in successfully",
        status: "success",
        duration: 5000,
        isClosable: true,
      });

      // Optionally reset form or redirect
    } catch (error) {
      console.error("Check-in error:", error);
      toast({
        title: "Check-in Failed",
        description: error instanceof Error ? error.message : "An error occurred during check-in",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  if (isLoadingMembers || isLoadingServices) {
    return (
      <Center h="50vh">
        <VStack spacing={4}>
          <Spinner size="xl" />
          <Text>Loading attendance system...</Text>
        </VStack>
      </Center>
    );
  }

  if (error) {
    return (
      <Box p={6}>
        <Alert status="error">
          <AlertIcon />
          <AlertTitle>Error Loading Data</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </Box>
    );
  }

  return (
    <Box p={6} maxW="600px" mx="auto">
      <VStack spacing={6} align="stretch">
        <Box textAlign="center">
          <Heading size="lg" mb={2}>
            Manual Attendance Check-In
          </Heading>
          <Text color="gray.600">
            Select a service and check in members individually or as families
          </Text>
        </Box>

        <FormControl>
          <FormLabel>Select Service</FormLabel>
          <Select
            value={selectedServiceId}
            onChange={(e) => setSelectedServiceId(e.target.value)}
            placeholder="Choose a service"
          >
            {services.map((service) => (
              <option key={service.id} value={service.id}>
                {service.name} - {new Date(service.date).toLocaleDateString()}
                {service.location && ` (${service.location})`}
              </option>
            ))}
          </Select>
        </FormControl>

        {selectedServiceId && (
          <Box>
            <CheckInForm
              members={members}
              serviceId={selectedServiceId}
              onSubmit={handleCheckInSubmit}
            />
          </Box>
        )}

        {!selectedServiceId && (
          <Alert status="info">
            <AlertIcon />
            <AlertTitle>Select a Service</AlertTitle>
            <AlertDescription>
              Please select a service from the dropdown above to begin checking in members.
            </AlertDescription>
          </Alert>
        )}
      </VStack>
    </Box>
  );
}
