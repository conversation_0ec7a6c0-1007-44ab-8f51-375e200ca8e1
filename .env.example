# Database
DATABASE_URL="postgresql://user:password@localhost:5432/chms"

# Application URLs
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-min-32-chars"

# OAuth Providers
# Get these from Google Cloud Console:
# https://console.cloud.google.com/apis/credentials
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Claude AI
ANTHROPIC_API_KEY="your-anthropic-api-key"

# Optional OAuth Providers
GITHUB_ID="your-github-id"
GITHUB_SECRET="your-github-secret"

FACEBOOK_ID="your-facebook-id"
FACEBOOK_SECRET="your-facebook-secret"

TWITTER_ID="your-twitter-id"
TWITTER_SECRET="your-twitter-secret"

# Development Settings
NEXT_PUBLIC_AUTO_LOGIN=true  # Set to 'true' to enable automatic login in development
