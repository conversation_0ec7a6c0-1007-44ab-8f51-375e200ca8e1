import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import ImportUsers from '@/components/users/ImportUsers';

// Mock fetch globally
global.fetch = vi.fn();

const mockFetch = fetch as vi.MockedFunction<typeof fetch>;

// Wrapper component for Chakra UI
const ChakraWrapper = ({ children }: { children: React.ReactNode }) => (
  <ChakraProvider>{children}</ChakraProvider>
);

describe('ImportUsers Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('When component loads', () => {
    it('should display the import form', () => {
      render(
        <ChakraWrapper>
          <ImportUsers />
        </ChakraWrapper>
      );

      expect(screen.getByText('Import Users')).toBeInTheDocument();
      expect(screen.getByText('Download Template')).toBeInTheDocument();
      expect(screen.getByText('Upload CSV File')).toBeInTheDocument();
    });

    it('should display file input and import button', () => {
      render(
        <ChakraWrapper>
          <ImportUsers />
        </ChakraWrapper>
      );

      expect(screen.getByTestId('file-input')).toBeInTheDocument();
      expect(screen.getByTestId('import-button')).toBeInTheDocument();
      expect(screen.getByTestId('download-template-button')).toBeInTheDocument();
    });
  });

  describe('When interacting with form', () => {
    it('should have import button disabled initially', () => {
      render(
        <ChakraWrapper>
          <ImportUsers />
        </ChakraWrapper>
      );

      const importButton = screen.getByTestId('import-button');
      expect(importButton).toBeDisabled();
    });

    it('should allow template download', () => {
      render(
        <ChakraWrapper>
          <ImportUsers />
        </ChakraWrapper>
      );

      const downloadButton = screen.getByTestId('download-template-button');
      expect(downloadButton).toBeEnabled();
      expect(downloadButton).toHaveTextContent('Download Template');
    });
  });
});
