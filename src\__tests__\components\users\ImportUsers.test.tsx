import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import ImportUsers from '@/components/users/ImportUsers';

// Mock fetch globally
global.fetch = vi.fn();

const mockFetch = fetch as vi.MockedFunction<typeof fetch>;

// Wrapper component for Chakra UI
const ChakraWrapper = ({ children }: { children: React.ReactNode }) => (
  <ChakraProvider>{children}</ChakraProvider>
);

describe('ImportUsers Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders import form', () => {
    render(
      <ChakraWrapper>
        <ImportUsers />
      </ChakraWrapper>
    );
    expect(screen.getByText(/Import Users/i)).toBeInTheDocument();
  });

  it('displays AI welcome message section', () => {
    render(
      <ChakraWrapper>
        <ImportUsers />
      </ChakraWrapper>
    );

    expect(screen.getByText('AI Welcome Message Generator')).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/describe the welcome message/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /generate welcome message/i })).toBeInTheDocument();
  });

  it('generates welcome message successfully', async () => {
    const mockResponse = {
      success: true,
      result: 'Welcome to our church community! We are excited to have you join us.',
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockResponse,
    } as Response);

    render(
      <ChakraWrapper>
        <ImportUsers />
      </ChakraWrapper>
    );

    const promptInput = screen.getByPlaceholderText(/describe the welcome message/i);
    fireEvent.change(promptInput, {
      target: { value: 'Generate a warm welcome message for new church members' },
    });

    const generateButton = screen.getByRole('button', { name: /generate welcome message/i });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/claude/task', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          description: 'Generate a warm welcome message for new church members',
          complexity: 'low',
        }),
      });
    });

    await waitFor(() => {
      expect(screen.getByText(/welcome to our church community/i)).toBeInTheDocument();
    });
  });

  it('handles AI generation errors', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      json: async () => ({
        error: 'AI service unavailable',
      }),
    } as Response);

    render(
      <ChakraWrapper>
        <ImportUsers />
      </ChakraWrapper>
    );

    const promptInput = screen.getByPlaceholderText(/describe the welcome message/i);
    fireEvent.change(promptInput, {
      target: { value: 'Generate a welcome message' },
    });

    const generateButton = screen.getByRole('button', { name: /generate welcome message/i });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(screen.getByText(/ai service unavailable/i)).toBeInTheDocument();
    });
  });
});
