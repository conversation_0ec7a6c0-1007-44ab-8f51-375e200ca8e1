@mvp @p1 @ai-integration @ai
Feature: AI-Generated Welcome Messages
  As a Church Administrator
  I want AI to generate personalized welcome messages for new members
  So that each member receives a warm, personalized greeting that reflects our church's values

  Background:
    Given the ChMS application is running
    And the Claude AI integration is configured
    And the AI welcome message system is enabled
    And I have appropriate permissions to manage member communications

  @p1 @smoke @ai
  Scenario: Generate welcome message for new member import
    Given I am importing new members via CSV
    And the CSV contains member "<PERSON>" with details:
      | Field       | Value                    |
      | firstName   | Sarah                    |
      | lastName    | Johnson                  |
      | email       | <EMAIL>          |
      | age         | 28                       |
      | interests   | Youth ministry, music    |
    When the member import process completes
    Then an AI-generated welcome message should be created for <PERSON>
    And the message should be personalized with her name
    And the message should reference her interests appropriately
    And the message should reflect our church's welcoming tone
    And the message should be stored in her member profile

  @p1 @ai
  Scenario: AI welcome message content quality
    Given a new member "<PERSON>" has been added
    And his profile includes:
      | Field          | Value                           |
      | age            | 35                              |
      | family_status  | Married with 2 children         |
      | interests      | Community service, Bible study  |
      | background     | New to the area                 |
    When the AI generates a welcome message
    Then the message should:
      | Quality Aspect    | Requirement                                    |
      | Personalization   | Include Michael's name and relevant details   |
      | Tone             | Be warm, welcoming, and pastoral              |
      | Length           | Be between 100-300 words                      |
      | Relevance        | Reference his interests and family situation  |
      | Church Values    | Reflect our church's mission and values       |
      | Call to Action   | Include next steps for engagement             |

  @p1 @ai
  Scenario: Bulk welcome message generation
    Given I have imported 25 new members
    And each member has different profile information
    When the AI welcome message generation process runs
    Then unique welcome messages should be generated for all 25 members
    And each message should be personalized to the individual member
    And no two messages should be identical
    And the generation should complete within 5 minutes
    And I should see "25 welcome messages generated successfully"

  @p0 @ai
  Scenario: AI service unavailable fallback
    Given I am importing new members
    And the Claude AI service is temporarily unavailable
    When the member import process attempts to generate welcome messages
    Then the members should still be imported successfully
    And I should see "Members imported. Welcome messages will be generated when AI service is available"
    And the members should be flagged for welcome message generation retry
    And a background job should retry message generation when service returns

  @p1 @ai
  Scenario: Welcome message customization and approval
    Given AI has generated a welcome message for new member "Lisa Chen"
    And I am reviewing the generated message
    When I view the message in the admin panel
    Then I should be able to edit the message before sending
    And I should be able to approve or reject the message
    And I should be able to regenerate the message if needed
    And I should be able to add personal notes from pastoral staff

  @p1 @ai
  Scenario: Welcome message delivery tracking
    Given a welcome message has been generated for "David Wilson"
    And the message has been approved for sending
    When the message is sent via email
    Then the delivery should be tracked and logged
    And I should see the delivery status in David's profile
    And the system should track:
      | Metric          | Value                    |
      | Generated Date  | Current timestamp        |
      | Approved By     | Admin user name          |
      | Sent Date       | Email send timestamp     |
      | Delivery Status | Delivered/Failed/Pending |

  @p2 @ai
  Scenario: AI message template customization
    Given I want to customize the AI welcome message style
    When I access the AI message settings
    And I update the message template with:
      | Setting           | Value                                    |
      | Church Name       | Grace Community Church                  |
      | Pastor Name       | Pastor Johnson                          |
      | Church Values     | Love, Community, Service, Growth        |
      | Tone              | Warm but professional                   |
      | Include Scripture | Yes, relevant verses                    |
    And I save the template settings
    Then future AI-generated messages should use the updated template
    And the messages should reflect the customized church information

  @p2 @ai
  Scenario: Welcome message analytics and effectiveness
    Given welcome messages have been sent to multiple new members
    When I view the welcome message analytics dashboard
    Then I should see metrics including:
      | Metric                    | Description                           |
      | Messages Generated        | Total AI-generated messages          |
      | Messages Sent            | Successfully delivered messages       |
      | Open Rate               | Percentage of messages opened         |
      | Response Rate           | Members who responded to messages     |
      | Engagement Score        | Overall engagement with new members   |
    And I should be able to filter analytics by date range

  @p2 @ai
  Scenario: Multi-language welcome message generation
    Given our church serves a diverse community
    And a new member "Marie Dubois" has French as preferred language
    When the AI generates a welcome message for Marie
    Then the message should be generated in French
    And the message should maintain cultural appropriateness
    And the message should include French church service information
    And the system should support multiple languages:
      | Language | Code | Supported |
      | English  | en   | Yes       |
      | French   | fr   | Yes       |
      | Spanish  | es   | Yes       |

  @p1 @ai
  Scenario: Welcome message error handling and retry
    Given the AI is generating a welcome message for "Robert Taylor"
    And the AI service returns an error during generation
    When the error occurs
    Then the system should log the error details
    And the member should still be processed successfully
    And the system should automatically retry message generation
    And after 3 failed attempts, it should flag for manual review
    And I should be notified of persistent AI service issues
