@mvp @p2 @financial @africa
Feature: Giving and Financial Tracking System
  As a Church Treasurer or Administrator
  I want to track member giving and church finances
  So that I can maintain accurate financial records and provide proper stewardship reporting

  Background:
    Given I am logged in as a Church Treasurer or Administrator
    And I have permissions to manage financial data
    And the financial tracking system is configured
    And appropriate security measures are in place

  @p2 @smoke
  Scenario: Record member offering/tithe
    Given I need to record Sunday offering collections
    When I navigate to "Financial" > "Record Giving"
    And I enter giving details:
      | Field           | Value                    |
      | Service Date    | Sunday, October 15, 2023 |
      | Service Type    | Morning Worship          |
      | Collection Type | Tithe & Offering         |
      | Total Amount    | ₦125,000                |
      | Currency        | Nigerian Naira           |
      | Collection Method| Cash                    |
    And I record individual contributions:
      | Member Name    | Amount  | Type     | Method | Envelope # |
      | <PERSON>     | ₦15,000 | Tithe    | Cash   | 001        |
      | <PERSON>   | ₦8,000  | Offering | Cash   | 002        |
      | Anonymous      | ₦5,000  | Offering | Cash   | -          |
    And I click "Save Giving Records"
    Then the giving records should be saved successfully
    And member giving histories should be updated
    And the total should match the collection amount

  @p2 @africa
  Scenario: Mobile money giving integration
    Given mobile money is popular in our region
    When I set up mobile money giving options:
      | Provider    | Account Number | Account Name           |
      | MTN MoMo    | **********    | Grace Community Church |
      | Vodafone    | **********    | Grace Community Church |
      | AirtelTigo  | **********    | Grace Community Church |
    And a member makes a mobile money donation:
      | Field           | Value                    |
      | Member          | Sarah Wilson             |
      | Amount          | GH₵50.00                |
      | Provider        | MTN MoMo                 |
      | Transaction ID  | ***************          |
      | Purpose         | Sunday Offering          |
    And I verify and record the transaction
    Then the mobile money giving should be recorded
    And the member's giving history should be updated
    And a receipt should be generated

  @p2
  Scenario: Pledge campaign management
    Given we are running a building fund campaign
    When I create a new pledge campaign:
      | Field           | Value                           |
      | Campaign Name   | New Sanctuary Building Fund    |
      | Goal Amount     | ₦50,000,000                   |
      | Start Date      | January 1, 2024                |
      | End Date        | December 31, 2024              |
      | Description     | Funds for new sanctuary construction |
    And members make pledges:
      | Member Name    | Pledge Amount | Payment Schedule | First Payment |
      | John Smith     | ₦500,000     | Monthly         | January 15    |
      | Mary Johnson   | ₦300,000     | Quarterly       | March 31      |
      | Bob Wilson     | ₦200,000     | One-time        | February 1    |
    Then the pledge campaign should track commitments and payments
    And I should see campaign progress toward the goal
    And members should receive pledge reminders

  @p2
  Scenario: Giving statement generation
    Given members need annual giving statements
    When I generate giving statements for the year 2023:
      | Member Name    | Total Giving | Tithe    | Offering | Special Gifts |
      | John Smith     | ₦180,000    | ₦150,000 | ₦20,000  | ₦10,000      |
      | Mary Johnson   | ₦96,000     | ₦80,000  | ₦12,000  | ₦4,000       |
      | Bob Wilson     | ₦240,000    | ₦200,000 | ₦25,000  | ₦15,000      |
    And I include tax-deductible information where applicable
    And I generate PDF statements
    Then each member should receive their personalized giving statement
    And statements should include all required tax information
    And statements should be securely delivered

  @p2
  Scenario: Financial reporting and analytics
    Given I need to prepare financial reports for church leadership
    When I generate financial reports for Q3 2023:
      | Report Type        | Period    | Details                    |
      | Income Summary     | Q3 2023   | All giving categories      |
      | Expense Report     | Q3 2023   | All expense categories     |
      | Budget vs Actual   | Q3 2023   | Comparison analysis        |
      | Giving Trends      | Q3 2023   | Member giving patterns     |
    Then I should see comprehensive financial data:
      | Category           | Q3 2023    | Q2 2023    | Change    |
      | Tithes            | ₦2,400,000 | ₦2,200,000 | +9.1%     |
      | Offerings         | ₦800,000   | ₦750,000   | +6.7%     |
      | Special Gifts     | ₦300,000   | ₦450,000   | -33.3%    |
      | Total Income      | ₦3,500,000 | ₦3,400,000 | +2.9%     |
    And reports should be formatted for board presentation

  @p2
  Scenario: Expense tracking and budget management
    Given I need to track church expenses against budget
    When I record monthly expenses:
      | Category          | Budgeted   | Actual     | Variance  |
      | Utilities         | ₦150,000   | ₦145,000   | -₦5,000   |
      | Staff Salaries    | ₦800,000   | ₦800,000   | ₦0        |
      | Ministry Programs | ₦200,000   | ₦225,000   | +₦25,000  |
      | Maintenance       | ₦100,000   | ₦85,000    | -₦15,000  |
      | Outreach         | ₦150,000   | ₦175,000   | +₦25,000  |
    And I analyze budget variances
    Then I should see budget performance analysis
    And I should receive alerts for significant variances
    And I should be able to adjust future budget allocations

  @p2
  Scenario: Online giving platform integration
    Given we want to offer online giving options
    When I integrate with online giving platforms:
      | Platform      | Features                    | Transaction Fee |
      | Paystack      | Card payments, bank transfer| 1.5% + ₦100    |
      | Flutterwave   | Multiple payment methods    | 1.4% + ₦100    |
      | Bank Transfer | Direct bank deposits        | ₦50 flat fee    |
    And members use online giving:
      | Member Name    | Amount   | Method        | Platform    |
      | Sarah Wilson   | ₦25,000  | Debit Card    | Paystack    |
      | Mike Brown     | ₦15,000  | Bank Transfer | Flutterwave |
      | Lisa Davis     | ₦10,000  | Direct Deposit| Bank        |
    Then online giving should be automatically recorded
    And members should receive digital receipts
    And giving should sync with member records

  @p2
  Scenario: Financial audit trail and compliance
    Given we need to maintain proper financial records
    When I review the financial audit trail:
      | Transaction Type | Date       | Amount    | Recorded By    | Approved By   |
      | Offering Record  | 2023-10-15 | ₦125,000  | Treasurer Jane | Pastor John   |
      | Expense Payment  | 2023-10-16 | ₦45,000   | Admin Sarah    | Treasurer Jane|
      | Budget Adjustment| 2023-10-17 | ₦10,000   | Pastor John    | Board Chair   |
    Then all financial transactions should have proper documentation
    And changes should be tracked with user attribution
    And the system should maintain compliance with financial regulations
    And audit reports should be available for external review

  @p2
  Scenario: Donor privacy and data protection
    Given donor information must be kept confidential
    When I manage giving data:
      | Privacy Setting    | Configuration                        |
      | Access Control     | Role-based permissions only          |
      | Data Encryption    | All financial data encrypted         |
      | Anonymous Giving   | Option to give without identification |
      | Data Retention     | 7-year retention policy              |
    And I generate reports with privacy protection:
      | Report Type        | Privacy Level                        |
      | Public Summary     | No individual donor information      |
      | Board Report       | Aggregate data only                  |
      | Tax Statements     | Individual data for recipients only  |
    Then donor privacy should be maintained at all times
    And access to financial data should be properly controlled
    And compliance with data protection regulations should be ensured

  @p2 @africa
  Scenario: Multi-currency giving support
    Given our church receives international support
    When I configure multi-currency giving:
      | Currency | Exchange Rate | Local Equivalent |
      | USD      | 1 USD = ₦750  | Auto-update     |
      | EUR      | 1 EUR = ₦820  | Auto-update     |
      | GBP      | 1 GBP = ₦950  | Auto-update     |
    And international donations are received:
      | Donor Name     | Amount | Currency | Local Equivalent |
      | John Anderson  | $200   | USD      | ₦150,000        |
      | Marie Dubois   | €150   | EUR      | ₦123,000        |
    Then multi-currency donations should be properly recorded
    And exchange rates should be tracked for accurate reporting
    And donors should receive receipts in their original currency
