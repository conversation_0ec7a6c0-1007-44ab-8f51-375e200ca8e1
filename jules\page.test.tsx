import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ManualCheckInPage from '@/app/attendance/manual-check-in/page'; // Adjust path
import { ChakraProvider, extendTheme } from '@chakra-ui/react';

// Mock Chakra UI
const mockUseToast = vi.fn();
vi.mock('@chakra-ui/react', async () => {
  const actualChakra = await vi.importActual('@chakra-ui/react');
  return {
    ...actualChakra,
    useToast: () => mockUseToast,
  };
});

// Mock child component CheckInForm
vi.mock('@/components/attendance/CheckInForm', () => ({
  __esModule: true,
  default: vi.fn(({ members, serviceId, onSubmit }) => (
    <form data-testid="mock-checkin-form" onSubmit={(e) => {
      e.preventDefault();
      // Simulate a simple submission for testing purposes
      if (serviceId && members.length > 0) {
        onSubmit({ memberId: members[0].id, type: 'INDIVIDUAL', serviceId });
      }
    }}>
      <input type="hidden" name="serviceId" value={serviceId} />
      <button type="submit">Mock Submit CheckInForm</button>
    </form>
  )),
}));


// Mock fetch
global.fetch = vi.fn();

const theme = extendTheme({});

describe('ManualCheckInPage', () => {
  const mockMembers = [
    { id: 'mem1', name: 'John Doe', firstName: 'John', lastName: 'Doe' },
    { id: 'mem2', name: 'Jane Smith', firstName: 'Jane', lastName: 'Smith' },
  ];
  const mockServices = [
    { id: 'serv1', name: 'Sunday Service', date: new Date().toISOString() },
    { id: 'serv2', name: 'Midweek Bible Study', date: new Date().toISOString() },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (global.fetch as vi.Mock).mockReset();
    mockUseToast.mockClear();
  });

  const renderPage = () => {
    render(
      <ChakraProvider theme={theme}>
        <ManualCheckInPage />
      </ChakraProvider>
    );
  };

  it('renders loading state initially', () => {
    (global.fetch as vi.Mock).mockImplementation(() => new Promise(() => {})); // Keep it pending
    renderPage();
    expect(screen.getByText('Loading data...')).toBeInTheDocument();
  });

  it('fetches members and services on mount and displays them', async () => {
    (global.fetch as vi.Mock)
      .mockResolvedValueOnce({ // Members fetch
        ok: true,
        json: async () => mockMembers,
      })
      .mockResolvedValueOnce({ // Services fetch
        ok: true,
        json: async () => mockServices,
      });

    renderPage();

    await waitFor(() => expect(screen.getByLabelText('Select Service')).toBeInTheDocument());
    expect(screen.getByDisplayValue('Choose a service')).toBeInTheDocument(); // Placeholder
    
    // Check if services are in the dropdown
    mockServices.forEach(service => {
      expect(screen.getByText(new RegExp(`${service.name} - .*`))).toBeInTheDocument();
    });
    
    // Check if CheckInForm mock is rendered (implies members were loaded too)
    await waitFor(() => expect(screen.getByTestId('mock-checkin-form')).toBeInTheDocument());
  });

  it('handles error when fetching members', async () => {
    (global.fetch as vi.Mock)
      .mockResolvedValueOnce({ ok: false, json: async () => ({ error: 'Failed to fetch members' }) }) // Members fetch fails
      .mockResolvedValueOnce({ ok: true, json: async () => mockServices }); // Services fetch succeeds

    renderPage();
    await waitFor(() => expect(screen.getByText('Failed to fetch members')).toBeInTheDocument());
  });

  it('handles error when fetching services', async () => {
    (global.fetch as vi.Mock)
      .mockResolvedValueOnce({ ok: true, json: async () => mockMembers }) // Members fetch succeeds
      .mockResolvedValueOnce({ ok: false, json: async () => ({ error: 'Failed to fetch services' }) }); // Services fetch fails

    renderPage();
    await waitFor(() => expect(screen.getByText('Failed to fetch services')).toBeInTheDocument());
  });

  it('submits check-in data when service is selected and form is submitted', async () => {
    (global.fetch as vi.Mock)
      .mockResolvedValueOnce({ ok: true, json: async () => mockMembers }) // Members
      .mockResolvedValueOnce({ ok: true, json: async () => mockServices }) // Services
      .mockResolvedValueOnce({ ok: true, json: async () => ({ message: 'Check-in successful' }) }); // Check-in API

    renderPage();

    // Wait for data to load
    await waitFor(() => expect(screen.getByLabelText('Select Service')).toBeInTheDocument());
    
    // Select a service
    const serviceSelect = screen.getByLabelText('Select Service');
    await userEvent.selectOptions(serviceSelect, mockServices[0].id);
    await waitFor(() => expect((serviceSelect as HTMLSelectElement).value).toBe(mockServices[0].id));


    // Submit the mocked CheckInForm
    // The mock CheckInForm automatically calls onSubmit with the first member and selected service
    const mockSubmitButton = screen.getByText('Mock Submit CheckInForm');
    fireEvent.click(mockSubmitButton);

    await waitFor(() => expect(global.fetch).toHaveBeenCalledWith('/api/attendance/check-in', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        memberId: mockMembers[0].id,
        type: 'INDIVIDUAL', // Mocked form submits as individual
        serviceId: mockServices[0].id,
      }),
    }));
    expect(mockUseToast).toHaveBeenCalledWith(expect.objectContaining({ status: 'success', title: 'Check-in Successful' }));
  });
  
  it('shows error toast if check-in API call fails', async () => {
    (global.fetch as vi.Mock)
      .mockResolvedValueOnce({ ok: true, json: async () => mockMembers })
      .mockResolvedValueOnce({ ok: true, json: async () => mockServices })
      .mockResolvedValueOnce({ ok: false, json: async () => ({ error: 'API check-in error' }) }); // Check-in API fails

    renderPage();
    await waitFor(() => expect(screen.getByLabelText('Select Service')).toBeInTheDocument());
    
    const serviceSelect = screen.getByLabelText('Select Service');
    await userEvent.selectOptions(serviceSelect, mockServices[0].id);

    const mockSubmitButton = screen.getByText('Mock Submit CheckInForm');
    fireEvent.click(mockSubmitButton);

    await waitFor(() => expect(mockUseToast).toHaveBeenCalledWith(expect.objectContaining({ status: 'error', title: 'Check-in Failed', description: 'API check-in error' })));
  });

  it('disables service select if no services are loaded', async () => {
    (global.fetch as vi.Mock)
      .mockResolvedValueOnce({ ok: true, json: async () => mockMembers })
      .mockResolvedValueOnce({ ok: true, json: async () => [] }); // No services

    renderPage();
    await waitFor(() => {
      const serviceSelect = screen.getByLabelText('Select Service') as HTMLSelectElement;
      expect(serviceSelect.disabled).toBe(true);
    });
    expect(screen.getByText('No services available.')).toBeInTheDocument();
  });

   it('shows message if no members are loaded and does not render CheckInForm', async () => {
    (global.fetch as vi.Mock)
      .mockResolvedValueOnce({ ok: true, json: async () => [] }) // No members
      .mockResolvedValueOnce({ ok: true, json: async () => mockServices });

    renderPage();
    await waitFor(() => expect(screen.getByText('No members found. Please add members before attempting check-in.')).toBeInTheDocument());
    expect(screen.queryByTestId('mock-checkin-form')).not.toBeInTheDocument();
  });

});

[end of src/__tests__/app/attendance/manual-check-in/page.test.tsx]
