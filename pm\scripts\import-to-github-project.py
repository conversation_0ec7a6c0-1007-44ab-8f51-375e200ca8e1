import csv
import subprocess
import time
import json

# --- CONFIGURATION ---
PROJECT_ID = "PVT_kwDODRQRUM4A9LK-"
CSV_FILE = "pm/scripts/github-projects-import.csv"
# TODO: Fill in your Author field ID after creating the field in GitHub Projects
AUTHOR_FIELD_ID = "PASTE_YOUR_AUTHOR_FIELD_ID_HERE"

# --- FUNCTIONS ---
def run_gh_api(query, variables):
    args = ["gh", "api", "graphql", "-f", f"query={query}"]
    for k, v in variables.items():
        args.extend(["-f", f"{k}={v}"])
    result = subprocess.run(args, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error running gh api: {result.stderr}")
        return None
    return result.stdout

def add_draft_issue(title, body):
    query = '''
    mutation($projectId:ID!, $title:String!, $body:String!) {
      addProjectV2DraftIssue(input: {
        projectId: $projectId
        title: $title
        body: $body
      }) {
        projectItem { id }
      }
    }
    '''
    variables = {"projectId": PROJECT_ID, "title": title, "body": body}
    output = run_gh_api(query, variables)
    if not output:
        return None
    data = json.loads(output)
    return data["data"]["addProjectV2DraftIssue"]["projectItem"]["id"]

def set_author_field(item_id, author):
    if not AUTHOR_FIELD_ID or AUTHOR_FIELD_ID == "PASTE_YOUR_AUTHOR_FIELD_ID_HERE":
        print("[!] Please set AUTHOR_FIELD_ID in the script before running.")
        return
    query = '''
    mutation($projectId:ID!, $itemId:ID!, $fieldId:ID!, $value:String!) {
      updateProjectV2ItemFieldValue(input: {
        projectId: $projectId
        itemId: $itemId
        fieldId: $fieldId
        value: { text: $value }
      }) {
        projectV2Item { id }
      }
    }
    '''
    variables = {
        "projectId": PROJECT_ID,
        "itemId": item_id,
        "fieldId": AUTHOR_FIELD_ID,
        "value": author
    }
    run_gh_api(query, variables)

# --- MAIN SCRIPT ---
with open(CSV_FILE, newline='', encoding='utf-8') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        if row.get("Status", "").strip().lower() == "done":
            continue
        title = row["Title"]
        notes = row.get("Notes", "")
        author = row.get("Author", "")
        print(f"Adding: {title} (Author: {author})")
        item_id = add_draft_issue(title, notes)
        if item_id and author:
            set_author_field(item_id, author)
        time.sleep(1)  # Avoid hitting rate limits

print("All items added to GitHub Project!") 