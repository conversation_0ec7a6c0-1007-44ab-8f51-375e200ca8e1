# Project Management (`pm/`)

This directory contains project management artifacts, planning documents, and specifications for the ChMS project.

## Contents

- **/adrs:** Architecture Decision Records documenting significant technical decisions.
  - `template.md`: Template for creating new ADRs.
- **/features:** Detailed requirements for specific features.
  - `attendance-frd.md`: Feature Requirements Document for the Attendance Management system.
- **/security:** Security-related documentation and policies.
  - `middleware.md`: Documentation on security middleware.
  - `security-standards.md`: Security standards and best practices.
- `prd.md`: Product Requirements Document outlining project goals, scope, and high-level features.
- `user-stories.md`: User stories derived from the PRD.
- `technical-architecture.md`: Overview of the technical architecture, component structure, and data flow.
- `deployment-manual.md`: Instructions for deploying the application.
- `security-policy.md`: Security policies and procedures.
- `project-overview.md`: High-level overview of the project.
- `roles-and-permissions.md`: Definition of user roles and their permissions.
- `testing-strategy.md`: Strategy for testing the application.
- `ui-elements-design-spec.md`: Specifications for UI elements.

## Related Documentation

- **Application Documentation (`docs/`):** Contains guides, standards, and tutorials for using and developing the application.
- **CONTRIBUTING.md (Root):** Guidelines for setting up the development environment and contributing to the project.
- **README.md (Root):** Overview of the entire project.

## Document Versioning

All documents in this directory follow semantic versioning (MAJOR.MINOR.PATCH):

- **MAJOR:** Significant changes that fundamentally alter the project direction or scope
- **MINOR:** New features or substantial updates to existing specifications
- **PATCH:** Minor updates, corrections, or clarifications

Each document should include a version history section at the end to track changes.
