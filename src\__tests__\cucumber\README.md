# Cucumber BDD Testing Setup

This directory contains the Cucumber.js setup for executable Gherkin testing in the ChMS project.

## Directory Structure

```
src/__tests__/cucumber/
├── step-definitions/          # Step definition files
│   ├── common-steps.ts       # Shared step definitions
│   ├── authentication-steps.ts # Authentication scenarios
│   ├── member-management-steps.ts # Member management scenarios
│   └── [feature]-steps.ts    # Feature-specific step definitions
├── support/                  # Support files
│   ├── world.ts             # Custom World class with test utilities
│   └── hooks.ts             # Before/After hooks for test setup
└── fixtures/                # Test data files
    └── members-import.csv   # Sample CSV for import testing
```

## Running Cucumber Tests

### Basic Commands

```bash
# Run all Cucumber tests
npm run cucumber

# Run smoke tests (P0 scenarios)
npm run cucumber:smoke

# Run P1 priority tests
npm run cucumber:p1

# Run Africa-specific tests
npm run cucumber:africa

# Run AI integration tests
npm run cucumber:ai

# Run tests in watch mode
npm run cucumber:watch

# Run BDD tests (alias for cucumber)
npm run test:bdd

# Run all tests (Vitest + Cucumber smoke)
npm run test:all
```

### Profile-Based Testing

The Cucumber configuration supports multiple profiles:

- **default**: All tests with standard settings
- **smoke**: P0 scenarios with fail-fast enabled
- **p1**: P1 priority scenarios
- **africa**: Africa-specific features with longer timeouts
- **ai**: AI integration tests with sequential execution

### Tag-Based Execution

You can run specific scenarios using tags:

```bash
# Run only smoke tests
npx cucumber-js --tags "@smoke"

# Run P1 tests excluding skipped ones
npx cucumber-js --tags "@p1 and not @skip"

# Run Africa-specific scenarios
npx cucumber-js --tags "@africa"

# Run AI integration tests
npx cucumber-js --tags "@ai"

# Run authentication tests only
npx cucumber-js --tags "@authentication"
```

## Test Reports

Cucumber generates multiple report formats:

- **HTML Report**: `reports/cucumber/cucumber-report.html`
- **JSON Report**: `reports/cucumber/cucumber-report.json`
- **Console Output**: Pretty formatted console output

### Viewing Reports

After running tests, open the HTML report in your browser:

```bash
# Windows
start reports/cucumber/cucumber-report.html

# macOS
open reports/cucumber/cucumber-report.html

# Linux
xdg-open reports/cucumber/cucumber-report.html
```

## Writing Step Definitions

### Basic Structure

```typescript
import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { CustomWorld } from '../support/world';

Given('I am on the {string} page', async function(this: CustomWorld, pageName: string) {
  await this.navigateTo(`/${pageName}`);
});

When('I click {string}', async function(this: CustomWorld, buttonText: string) {
  await this.page?.click(`button:has-text("${buttonText}")`);
});

Then('I should see {string}', async function(this: CustomWorld, text: string) {
  await expect(this.page?.locator(`text=${text}`)).toBeVisible();
});
```

### Using the Custom World

The `CustomWorld` class provides utilities for:

- **Browser automation** via Playwright
- **React component testing** via React Testing Library
- **Database mocking** via Prisma mock
- **Authentication helpers** for different user roles
- **Form helpers** for filling and submitting forms
- **API mocking** for external services

### Example Usage

```typescript
// Browser-based testing
await this.setupBrowser();
await this.navigateTo('/members');
await this.loginAs('admin');

// Component testing
this.renderComponent(<MemberList />);

// Database operations
await this.seedDatabase({ members: [...] });

// API mocking
this.mockApiResponse('/api/ai/claude', { message: 'Welcome!' });
```

## Integration with Existing Tests

Cucumber tests complement the existing Vitest unit tests:

- **Vitest**: Unit and integration tests for components and functions
- **Cucumber**: End-to-end behavior testing based on Gherkin scenarios
- **Playwright**: Browser automation for E2E scenarios
- **React Testing Library**: Component testing within Cucumber steps

## Best Practices

### 1. Step Definition Organization

- Keep step definitions focused on specific features
- Use common steps for shared behaviors
- Maintain consistent naming conventions

### 2. Test Data Management

- Use the `CustomWorld.testData` for scenario-specific data
- Leverage hooks for setup and teardown
- Mock external dependencies consistently

### 3. Scenario Writing

- Follow Given-When-Then structure
- Use descriptive scenario names
- Tag scenarios appropriately for filtering

### 4. Error Handling

- Take screenshots on failure
- Provide meaningful error messages
- Clean up resources in After hooks

## Troubleshooting

### Common Issues

1. **Browser not starting**: Check Playwright installation
2. **Step definitions not found**: Verify file paths in cucumber.config.js
3. **Timeout errors**: Increase timeout in world parameters
4. **Import errors**: Ensure TypeScript compilation is working

### Debug Mode

Run tests with debug output:

```bash
DEBUG=cucumber* npm run cucumber
```

### Headful Mode

Run browser tests in headful mode:

```bash
HEADLESS=false npm run cucumber
```

## Contributing

When adding new features:

1. Create corresponding Gherkin scenarios in `features/`
2. Implement step definitions in appropriate files
3. Add test data to fixtures if needed
4. Update this README with new patterns or utilities
