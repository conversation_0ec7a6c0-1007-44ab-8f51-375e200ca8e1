{"name": "chms", "version": "1.0.0", "description": "Church Management System - A modern web-based solution for church administration", "main": "src/index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "cucumber": "cucumber-js", "cucumber:smoke": "cucumber-js --profile smoke", "cucumber:p1": "cucumber-js --profile p1", "cucumber:africa": "cucumber-js --profile africa", "cucumber:ai": "cucumber-js --profile ai", "cucumber:watch": "cucumber-js --watch", "test:bdd": "npm run cucumber", "test:bdd:smoke": "npm run cucumber:smoke", "test:all": "npm run test && npm run cucumber:smoke"}, "dependencies": {"@chakra-ui/icons": "^2.0.0", "@chakra-ui/next-js": "^2.4.2", "@chakra-ui/react": "^2.10.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@node-rs/argon2": "^2.0.2", "@prisma/client": "^6.11.1", "@sentry/nextjs": "^9.35.0", "@tanstack/react-query": "^5.81.5", "@types/dompurify": "^3.0.5", "@types/formidable": "^3.4.5", "@types/qrcode": "^1.5.5", "@yudiel/react-qr-scanner": "^2.3.1", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "express-rate-limit": "^7.5.1", "focus-trap-react": "^11.0.4", "formidable": "^3.5.4", "framer-motion": "^12.23.0", "html5-qrcode": "^2.3.8", "ioredis": "^5.6.1", "next": "^14.2.3", "next-auth": "^4.24.7", "pino": "^9.7.0", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "recharts": "^3.0.2", "swr": "^2.3.4", "winston": "^3.17.0", "zod": "^3.25.74", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@cucumber/cucumber": "^11.3.0", "@cucumber/html-formatter": "^21.12.0", "@cucumber/pretty-formatter": "^1.0.1", "@playwright/test": "^1.53.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.0.0", "@types/jest-axe": "^3.5.9", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.4", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "axe-core": "^4.10.3", "csv-parse": "^5.6.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.3", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "msw": "^2.10.3", "node-mocks-http": "^1.17.2", "playwright": "^1.53.2", "prisma": "^6.11.1", "sharp": "^0.34.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "vitest-axe": "^0.1.0", "vitest-mock-extended": "^3.1.0"}, "author": "<PERSON>", "license": "MIT"}