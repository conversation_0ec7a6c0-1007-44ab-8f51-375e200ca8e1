@mvp @p0 @member-management @ai
Feature: Member CSV Import System
  As a Church Administrator
  I want to import members from CSV files
  So that I can efficiently add multiple members to the system with AI-generated welcome messages

  Background:
    Given I am logged in as a Church Administrator
    And I am on the Import Users page
    And the AI integration system is available
    And the member import API is functional

  @smoke @p0
  Scenario: Successful CSV import with AI welcome messages
    Given I have a valid CSV file with member data:
      | firstName | lastName | email           | phone        | dateOfBirth |
      | John      | Doe      | <EMAIL>  | +1234567890  | 1990-01-15  |
      | <PERSON>      | <PERSON>    | <EMAIL>  | +1234567891  | 1985-03-22  |
    When I upload the CSV file
    And I click the "Import Members" button
    Then the system should process all 2 members successfully
    And AI-generated welcome messages should be created for each member
    And I should see "Import completed successfully: 2 members added"
    And the members should appear in the member directory
    And each member should have a personalized welcome message

  @p0
  Scenario: Import validation with invalid CSV format
    Given I have an invalid CSV file with missing required columns
    When I upload the CSV file
    And I click the "Import Members" button
    Then I should see validation errors
    And the error should specify "Missing required columns: firstName, lastName, email"
    And no members should be imported
    And the import should be rejected

  @p0
  Scenario: Import with duplicate email addresses
    Given I have a CSV file with duplicate email addresses:
      | firstName | lastName | email           |
      | John      | Doe      | <EMAIL>  |
      | Jane      | Doe      | <EMAIL>  |
    When I upload the CSV file
    And I click the "Import Members" button
    Then I should see a validation error
    And the error should specify "Duplicate email found: <EMAIL>"
    And no members should be imported

  @p0
  Scenario: Import with existing member email
    Given a member with email "<EMAIL>" already exists in the system
    And I have a CSV file containing "<EMAIL>"
    When I upload the CSV file
    And I click the "Import Members" button
    Then I should see a conflict error
    And the error should specify "Email already exists: <EMAIL>"
    And the import should be rejected

  @p0
  Scenario: Template download functionality
    Given I am on the Import Users page
    When I click the "Download Template" button
    Then a CSV template file should be downloaded
    And the template should contain the correct column headers:
      | firstName | lastName | email | phone | dateOfBirth | address | membershipStatus |
    And the template should include example data rows
    And the file should be named "member-import-template.csv"

  @p1
  Scenario: Import progress tracking
    Given I have a large CSV file with 100 members
    When I upload the CSV file
    And I click the "Import Members" button
    Then I should see a progress indicator
    And the progress should update as members are processed
    And I should see "Processing member 25 of 100"
    And the import should complete with "100 members imported successfully"

  @p1 @ai
  Scenario: AI welcome message generation failure handling
    Given I have a valid CSV file with member data
    And the AI service is temporarily unavailable
    When I upload the CSV file
    And I click the "Import Members" button
    Then the members should still be imported successfully
    And I should see "Members imported. Welcome messages will be generated when AI service is available"
    And the members should be flagged for welcome message generation retry

  @p1
  Scenario: Partial import with some invalid records
    Given I have a CSV file with mixed valid and invalid records:
      | firstName | lastName | email           | phone        |
      | John      | Doe      | <EMAIL>  | +1234567890  |
      | Jane      |          | invalid-email   | invalid-phone|
      | Bob       | Smith    | <EMAIL>   | +1234567892  |
    When I upload the CSV file
    And I click the "Import Members" button
    Then valid records should be imported successfully
    And I should see "2 members imported, 1 record failed"
    And I should see detailed error information for failed records
    And I should be able to download an error report

  @p1
  Scenario: Import with optional fields
    Given I have a CSV file with optional member fields:
      | firstName | lastName | email           | address      | membershipStatus | notes        |
      | John      | Doe      | <EMAIL>  | 123 Main St  | Active          | New member   |
    When I upload the CSV file
    And I click the "Import Members" button
    Then the member should be imported with all provided fields
    And optional fields should be populated correctly
    And missing optional fields should be left empty

  @p2
  Scenario: Import file size validation
    Given I have a CSV file larger than the maximum allowed size (10MB)
    When I attempt to upload the CSV file
    Then I should see an error message "File size exceeds maximum limit of 10MB"
    And the file should not be uploaded
    And I should be advised to split the file into smaller chunks

  @p2
  Scenario: Import with special characters and international names
    Given I have a CSV file with international characters:
      | firstName | lastName  | email              |
      | José      | García    | <EMAIL>     |
      | 王        | 小明      | <EMAIL>     |
      | François  | Müller    | <EMAIL> |
    When I upload the CSV file
    And I click the "Import Members" button
    Then all members should be imported correctly
    And special characters should be preserved
    And AI welcome messages should handle international names appropriately
