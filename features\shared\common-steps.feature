# Shared Step Definitions and Common Scenarios
# This file contains reusable Gherkin steps and scenarios that are common across multiple features

@shared
Feature: Common Application Behaviors
  This feature defines shared behaviors and step definitions used across multiple ChMS features

  # Authentication Steps
  Background: Common Authentication Setup
    Given the ChMS application is running
    And the database is properly configured
    And user accounts exist in the system

  # Common Login Scenarios
  @shared @authentication
  Scenario Template: User login with different roles
    Given I am on the login page
    When I enter email "<email>" and password "<password>"
    And I click the "Sign In" button
    Then I should be logged in as "<role>"
    And I should see navigation appropriate for "<role>"

    Examples:
      | email                    | password      | role                |
      | <EMAIL>         | AdminPass123! | Administrator       |
      | <EMAIL>        | PastorPass123!| Pastor              |
      | <EMAIL>         | StaffPass123! | Staff Member        |
      | <EMAIL>       | GreetPass123! | Greeter             |

  # Common Data Validation
  @shared @validation
  Scenario Template: Email validation across forms
    Given I am on a form that requires email input
    When I enter email "<email_input>"
    And I submit the form
    Then I should see validation result "<result>"
    And the error message should be "<error_message>"

    Examples:
      | email_input           | result  | error_message                      |
      | <EMAIL>       | success | none                               |
      | invalid-email         | error   | Please enter a valid email address |
      | @invalid.com          | error   | Please enter a valid email address |
      | valid@                | error   | Please enter a valid email address |
      |                       | error   | Email is required                  |

  # Common Phone Number Validation
  @shared @validation
  Scenario Template: Phone number validation across forms
    Given I am on a form that requires phone input
    When I enter phone number "<phone_input>"
    And I submit the form
    Then I should see validation result "<result>"
    And the error message should be "<error_message>"

    Examples:
      | phone_input    | result  | error_message                        |
      | +1234567890    | success | none                                 |
      | 1234567890     | success | none                                 |
      | (************* | success | none                                 |
      | abc123         | error   | Please enter a valid phone number    |
      | 123            | error   | Please enter a valid phone number    |
      |                | error   | Phone number is required             |

  # Common Navigation
  @shared @navigation
  Scenario: Application navigation accessibility
    Given I am logged into the system
    When I navigate through the application using keyboard only
    Then all interactive elements should be accessible via keyboard
    And focus indicators should be clearly visible
    And the tab order should be logical
    And I should be able to access all main features

  # Common Error Handling
  @shared @error-handling
  Scenario: Network connectivity error handling
    Given I am using the application
    When the network connection is lost
    Then I should see "Connection lost" notification
    And the application should continue to function in offline mode where possible
    And when connection is restored
    Then I should see "Connection restored" notification
    And pending actions should sync automatically

  # Common Loading States
  @shared @ui
  Scenario: Loading state indicators
    Given I am performing an action that takes time to complete
    When the action is processing
    Then I should see appropriate loading indicators
    And the interface should remain responsive
    And I should be able to cancel the action if applicable
    And the loading state should not exceed reasonable time limits

  # Common Success/Error Messages
  @shared @messaging
  Scenario Template: System feedback messages
    Given I perform an action in the system
    When the action completes with status "<status>"
    Then I should see a message with type "<message_type>"
    And the message should be displayed for appropriate duration
    And the message should be dismissible
    And the message should be accessible to screen readers

    Examples:
      | status  | message_type |
      | success | success      |
      | error   | error        |
      | warning | warning      |
      | info    | info         |

  # Common Data Export
  @shared @export
  Scenario Template: Data export functionality
    Given I have permission to export "<data_type>" data
    And I am viewing the "<data_type>" list
    When I click "Export Data"
    And I select format "<format>"
    And I click "Download"
    Then a "<format>" file should be downloaded
    And the file should contain the expected data
    And the export should be logged for audit purposes

    Examples:
      | data_type | format |
      | members   | CSV    |
      | members   | PDF    |
      | visitors  | CSV    |
      | attendance| CSV    |

  # Common Search Functionality
  @shared @search
  Scenario: Universal search behavior
    Given I am on a page with search functionality
    When I enter search term "John"
    Then I should see results containing "John"
    And the search should be case-insensitive
    And the search should highlight matching terms
    And I should be able to clear the search
    And empty search should show all results

  # Common Pagination
  @shared @pagination
  Scenario: List pagination behavior
    Given I am viewing a list with more than 50 items
    Then the list should be paginated
    And I should see page navigation controls
    And I should see "Showing X of Y items"
    And I should be able to navigate between pages
    And the page size should be configurable
    And the current page should be clearly indicated

  # Common Responsive Design
  @shared @responsive
  Scenario Template: Responsive design across devices
    Given I am using the application on "<device_type>"
    When I navigate through different pages
    Then the layout should be optimized for "<device_type>"
    And all functionality should remain accessible
    And text should be readable without zooming
    And touch targets should be appropriately sized

    Examples:
      | device_type |
      | desktop     |
      | tablet      |
      | mobile      |
