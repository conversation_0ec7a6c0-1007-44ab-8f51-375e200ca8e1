import { Container, Heading, Text, VStack, Box, Divider } from '@chakra-ui/react';
import { ClaudeTaskForm } from '@/components/ClaudeTaskForm';
import Head from 'next/head';

export default function ClaudePage() {
  return (
    <>
      <Head>
        <title><PERSON> AI Assistant | ChMS</title>
        <meta name="description" content="Ask Claude AI questions or get assistance with tasks" />
      </Head>
      
      <Container maxW="container.lg" py={8}>
        <VStack spacing={6} align="stretch">
          <Box>
            <Heading size="lg" mb={2}>Claude AI Assistant</Heading>
            <Text color="gray.600">
              Ask <PERSON> any question or request assistance with tasks. <PERSON> can help with drafting emails, 
              generating content, answering questions about the system, or providing guidance on best practices.
            </Text>
          </Box>
          
          <Divider my={4} />
          
          <Box bg="white" p={6} borderRadius="md" boxShadow="sm">
            <ClaudeTaskForm />
          </Box>
          
          <Box mt={4}>
            <Text fontSize="sm" color="gray.500">
              Powered by Anthropic&apos;s Claude 3 Sonnet. Responses are generated by AI and may not always be accurate.
              Please review any generated content before using it in production.
            </Text>
          </Box>
        </VStack>
      </Container>
    </>
  );
}
