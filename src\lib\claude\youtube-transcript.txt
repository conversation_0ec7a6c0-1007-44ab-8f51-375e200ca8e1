Source: https://youtu.be/1L509JK8p1I 

TRANSCRIPT:
[Music] if you haven't been vi coding enough you must have experienced problem where you ask cursor to implement a small change but just map up your whole project or cursor is not aware of all the dependencies in your codebase and implement something that leads to loads of errors This is a very common issue of AI coding agent in general no matter which platform you're using But there's one technique that show promising improvements that will make your cursor make way less errors which is giving your AI coding agent a task management system It helps it to understand overall implementation plan and also control the amount of context goes into each step when it is implementing specific tasks I was able to build a fully functional multiplayer online drawing game where both players can draw image of given work and we send the result to GBD4 where it look at image and give evaluation and pick up the winner And this whole game is actually imprinted by cursor with just one shot without much arrow which is absolutely insane That's why today I want to show you what my workflow is and how can you adopt it for your own project And before you use any of those tools I just introduced people has been hacking together task management workflow to improve performance for a while At its core it basically means you ask cursor to break down your complex PRD into small tasks and have a document where cursor can have access to to track and maintain what tasks are coming and what tasks already been done And this is a quick example from Elle The most basic implementation is in your cursor project you will create a cursor rule and it looks something like this Basically rule where you tell cursor to always refer to task.md to keep track about what task they already been done and what task haven't And with this one we can just create a task.md file and give a prompt I want to build a x app help me breaking down into small task of our core me feature and add to task.md So cursor will create a list of task here and after cursor finish every single task it will just come back and mar those task as completed So it has a context of overall implementation plan With this method it already help a ton for executing complex task with cursor But tools like cloud taskmaster and boomeran task from ruk code bring even more sophisticated task management behavior into the AI coding agents For example for task master AI it is a command line package that you can run in cursor windsurf data where it utilize cloud 3.7 or more advanced model to look at PRD you have and break that down into small subtasks by running a simple command line like taskmaster parse PRD And what's really amazing about it is that it will breaking down tasks in logical order consider all the dependencies between different tasks So you won't have situation where cursor implements something but require other dependencies that hasn't been implemented yet It also has useful command line like analyze complexities to use perplexity and cloud to analyze how complicated each task is and if certain task complexity score is very high it will allow you to expand on those task further and by breaking down those complex tasks into even smaller bits The success rate of it delivering a functional application just increase dramatically And Roco's boomeran task also something similar It gives AI agents tools like new tasks to breaking down a complex project into small bits and keep track about progress Those tools have completely changed my workflow So I'm going to quickly take you through what's my new best practice vibe coding workflow with those new tools But before we dive into that I know many of you are trying to build AI agents for business But there are many pitfalls I saw people fell into at delivering successful production agents That's why I want to introduce you to this research HubSpot did where they interviewed tons of business and startups who have been launching AI agents for the past 12 months to understand which AI agent use case actually drove huge amount of business value and ROI versus ones that sounds fancy but actually very difficult to deliver value and which signal customers are deploying huge amount of budgets to buy AI agent solution They include lots of real world success stories and articulate those learnings into frameworks that you can use to build your next agents from which use cases more suitable for chatbot versus actual autopilot agents How do you determine which task is best for AI agents versus more traditional workflow automation as well as list of common pitfalls that many other people encounter including myself when deploying production agents and best practice of how other people resolve that like what's the best practice for you to build integration into existing systems This helped bring a lot of clarities and many mistakes I personally experienced So if you're planning to build agents I highly recommend you go and have a read and this is totally free I have put a link in the description below for you to download Now let's talk about my new AI coding workflow with task management systems Firstly let's talk about ru code's boomer task feature And if you don't know ru code ro code you can almost consider as a open-source cursor that live inside visual studio code It is totally free to use All you need to do is just provide your own entropy key and then it will just work like any other AI coding agent that you've been using But what's really cool about real code is that unlike cursor where you only have a few predefined agent mode Ro allow you to create your own modes like at default they will provide coding agent architect agent that will help you do the planning debug agent that help you figure out where the arrow is But you can also build custom modes like boomer wrench mode where it will be focusing on planning and breaking down the plan into smaller manageable pieces Think of it like delegating your work to specialized assistant Each subtask runs in its own context So I can choose a boomer range mode that I just customized and then say help me build a to-do app At top you can see that it will keep track about how many token consumption is as well as total amount of API cost and it will firstly delegate a planning task to the architect agent and this architect agent will have this system prompt where it will continuously confirm with me about the requirements and then it will start planning out the project breaking down into specific feature figure out things like user story key feature components project structure state management and many more So it has a full understanding about all dependencies between different functions I can give feedback in the middle and once the plan is finished and breaking down to small tasks it can switch to the code mode to start generating the code and the code agent will start executing different tasks based on the plan and then complete the actual application for me And you can see the result here is very high quality and it even have functionality built in where the agent will be able to run the application in the browser see the result to automate testing as well And with this one the result already feels better than what I got out of box from cursor But on the other hand cloud taskmaster integrate much more deeply into cursor and wings surfer First let's install the taskmaster AI You can open terminal in any folder and do npm install-g taskmaster-ai And once it's finished you can run a few different commands One will be taskmaster in it This will set up the project inside the folder So you can just do taskmaster in it directly But I would suggest you set up the project first Like if you're building a nextjs project with chessen you can just do this command And once it's done we can do cursor my app Inside here we can do taskmaster in it This will ask for the pent name and I'll just call it my app Description doesn't really matter You can skip all those stuff and then just let it set things up And what will happen now is that on the left side you firstly see it add a few cursor rule Some of them are generic one like this cursor rule basically teach cursor how can it add new cursor rules So as you go deep into the implementation you can ask it to reflect and creating rules about the mistake it make for example and it will follow this rules to create the next cursor rules and this self-improve is basically the same thing it kind of try to get cursor to do this proactively and the step workflow is where it teach cursor about all the commands it will need to actually check all the task in the backlog and if you're using windserve there will be windserve root here as well inside the scripts folder it provide a structure about what does a prd can look like but the most important one is that it will have thisv.exam example file What you need to do is swap out this entropic key as well as proplasticity key here So entropy is a model that will be used to break down your PRD into small tasks and they also use perlastity to do some research So if part of task is using a new package that just released then it will actually use perlasty to fetch the latest developer documents and include those into the task information So I recommend you add both API key here and once you did that we can start creating our PRD There are many different ways you can create PRDs If you're in the AI builder climate building already you will have access to tools like tanks coder the dev where it will help you generate PRD automatically and fill in all the gaps for the features that you might not think of So if you're already in the AI build club you can use this tool to get the PRD here But if you don't have access you can also just chat with cursor agent use that to help you generate PRD For example I can just say help me build an online game like Scribble but instead of a human guess word it will be large model guess a word So each round all users will be given a same word and they have 60 seconds to draw the image In the end all images will be sent to open 4 and let it choose which image is closest to the word Now play the role as the engineer manager help me think through what are the core features of implementing such game and then it will spit out the core functionalities Obviously I can chat back and forth but once I finish I can just say great now let's help me build the core MVP features requirements into prd.txe using the example uh prd.txe tx as reference which is what we have uh showing here and then you will see a prd has been created with good amount details and I will accept that So now since we have this prd generated the next step we can use this command taskmaster parse drd to breaking down this prd into small task and this is where the power of taskmaster begin So I will do this taskmaster parse prd scripts/prd.txt Okay so I had this arrow uh just need making sure you remove this example and let's do it again So now it will start creating task files based on this prd and you will see here where we have task folder It has all the tasks that created from taskmaster What we can do is we can do taskmaster list and this will show you the list of tasks that it has been created What's really cool about taskmaster task list is on the right side you can see here's a dependencies column So when breaking down tasks it will actually list out the task in a logical order and making sure there are clear dependencies mapped out So when it implement it can implement in the right order Meanwhile there are also some pretty useful commands You have this command called analyze complexity What this will do is that I can do task master and analyze complexity This was basically send all the task I created to cloud 3.7 as well as perplexity Basically ask it to just evaluate how complicated or how difficult it is to implement this feature And once it is done I can do taskmaster complexity report It will show me the evaluation of each task and its complexity score But what's really useful is for those complex tasks it also give you the prompt that you can copy At the moment you can't really copy this directly If the UI just break up but you can just copy the first one and go to complexity report and find the specific ID which is this one and we can copy the expansion prompt So here it generate a prompt detailed technical implementation of HTML 5 canva component including drawing tools input handling across all devices So I can just do this and now it breaking down that specific task into smaller ones And as we know once a complex task breaking down to smaller one it is more likely to succeed without any error And you can continue doing that um by adding another one for the task number five So you get the drill Um basically you can do this process back and forth for a few times until you're happy with the backlog here You can also do things like update as well So if later down the row you decide to like change the plan you can also do taskmaster update ID equal to like four and prompt equal to something like make sure we use three js And what will happen is that once you give this prompt it will actually update the whole plan based on this new instruction which is really really helpful But once it's done you can do taskmaster list with subtasks This will show all subtask here directly So it will make it easier for you to review all the task here So that's pretty much it Once we done this you can just go to the cursor agent and say let's start implementing the app based on task we created using taskmaster Let's check the next most important task first So you might have this arrow here that it is reusing the wrong command Uh so I will just tell it do not use script dev.js use taskmaster instead Just go list all task created and follow the plan Then it will see the plan and decide to implement the first one first And as it work on the task it will also set the status of this task to be in progress And here since I actually turn on the yolo mode of cursor which means it didn't ask me for permission for running any command lines I can basically just go drink a coffee and let it do the work And as it complete it will mark this task as done and move on to the next one by doing taskmaster next But now when you're in Euro mode uh I think cursor it will limit to 25 coursees if you're using the cloud 3.7 but you can actually put on Gemini 2.5 Pro Max This will allow you to skip this limit and just do like 2002 coursees without any pause here All right So it's actually pretty crazy It just continue executing tasks one up another and generate a whole bunch of files probably a few thousand lines of code And let's just run that first Um so I'll do mpm run def All right So if I try to open this it has a lobby building and authentication building So I can give a name choose avatar start playing and my user has been created I can set how much time we have for drawing the difficulty and whether it's public game or not So it's pretty good Uh I can create but okay looks like the actual game inside hasn't been done yet but it's pretty impressive about how much it is able to deliver in just one go And I can come back to do task master list with subtasks So you can see that it done the uh first four tasks but it haven't fully finished the development game run logic into my UI component Maybe that's why the actual game room is not showing up So I can ask it continue to implement but you saw that I had this arrow here and then I can prompt it now refract the errors you made and creating new cursor rules to making sure you don't make those mistakes again Now you can see that it actually adding new cursor rules about nextjs app router Uh though for some reason it didn't actually create content which is bit weird and accept this one So for now I just copy paste manually I probably to update cursor rules to making sure it will be saved properly But now I'm going to continue task Now let's check what is the next task to complete to a point where we can do some quick testing of the drawing Okay after another 15 minutes of it just doing the task by itself I got this game that's kind of fully functional where I can give it a name choose my avatar start playing and also create a room called Jason's room I can set a timer about how long people can draw as well as number of runs the difficulties and if I create a room other people will be able to see the room I created as well Click on join We will see multiple players in the room And if I start this I will have this canva where people can start drawing to describe what this uh word is And on the top right corner there's also a timer document how much time is left And once it finish it will send both result to chat GPT and GPT will look at the image and give the description and evaluation pick up the winner to get points and then move on to the next one So it's pretty amazing that it did a whole multiplayers games like this by itself in 20 minutes So this how much performance gain you can get by equip cursor with the right task management systems and what's really exciting is that this is just beginning I can imagine those tools and system became way better in a few months time I also interviewed the creator of taskmaster project where he gave more detailed breakdown about the best practice workflow to fully unleash the power of taskmaster and exciting things that they are working on right now If you're interested you can join the AI builder club where I put a full interview and workflow inside the community for you to check out as well as bunch other learnings and tips from industry experts for both vibe coding and building production ready AI agents And you will also have access to tools like 10x coderdev where it will help you generate bulletproof cursor PRD as well as next.js boplay that already have all payment backend database set up so you can launch your SAS in just a weekend I post a link in the description below for you to join if you're interested I hope you enjoyed this video I continue sharing new tips and workflows I learned 