generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id          String    @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  ministryUnits MinistryUnit[]
  users       User[]
  invitations Invitation[]
  settings    OrganizationSettings?
  members     Member[]
  visitors    Visitor[]
  services    Service[]
  classes     Class[]
  metrics     Metric[]
  reports     Report[]
  attendance  Attendance[]
  events      Event[]
  touchpoints OrganizationTouchpoint[]
}

// Enhanced Class model with type
enum ClassType {
  BAPTISM
  MARRIAGE
  MEMBERSHIP
  DISCIPLESHIP
  LEADERSHIP
  OTHER
}

model Class {
  id             String    @id @default(cuid())
  name           String
  type           ClassType @default(OTHER)
  description    String?
  startDate      DateTime?
  endDate        DateTime?
  organizationId String
  capacity       Int?
  requirements   String?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  organization   Organization @relation(fields: [organizationId], references: [id])
  sessions       ClassSession[]
  members        MemberClass[]
  attendance     Attendance[]
  instructors    ClassInstructor[]
}

model ClassSession {
  id          String       @id @default(cuid())
  classId     String
  date        DateTime
  notes       String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  class       Class     @relation(fields: [classId], references: [id])
  attendances Attendance[] @relation("ClassSessionAttendance")
}

model Member {
  id             String   @id @default(cuid())
  firstName      String
  lastName       String
  email          String?  @unique
  phone          String?
  dateOfBirth    DateTime?
  conversionDate DateTime?
  baptismDate    DateTime?
  weddingDate    DateTime?
  organizationId String
  familyId       String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization   Organization @relation(fields: [organizationId], references: [id])
  family         Family?      @relation(fields: [familyId], references: [id])
  classes        MemberClass[]
  attendance     Attendance[]
  ministryUnitLeadership MinistryUnitLeader[]
  ministryUnitMembership MinistryUnitMember[]

  @@index([organizationId])
  @@index([familyId])
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  attendances  Attendance[]
}

model User {
  id                String      @id @default(cuid())
  email             String      @unique
  name              String?
  password          String
  role              Role        @default(VIEWER)
  verificationToken String?
  emailVerified     DateTime?
  dateOfBirth       DateTime?
  memorableDates    Json?

  // Account Security
  failedLoginAttempts Int?      @default(0)
  lastFailedLoginAt   DateTime?
  lockedUntil         DateTime?
  lastLoginAt         DateTime?
  lastLoginIp         String?

  organization      Organization? @relation(fields: [organizationId], references: [id])
  organizationId    String?
  accounts          Account[]
  sessions          Session[]
  classInstructor   ClassInstructor[]
  auditLogs         AuditLog[]
  permissions       Permission[]
}

model Permission {
  id        String   @id @default(cuid())
  userId    String
  name      String
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
}

enum Role {
  SUPER_ADMIN
  ADMIN
  MANAGER
  STAFF
  MEMBER    // Make sure this is added
  VIEWER
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Ministry Unit related enums
enum MinistryUnitType {
  DEPARTMENT
  TEAM
  COMMITTEE
  MINISTRY
  CELL_GROUP
  OTHER
}

enum MinistryCategory {
  WORSHIP
  CHILDREN
  YOUTH
  ADULTS
  OUTREACH
  DISCIPLESHIP
  ADMINISTRATION
  PASTORAL
  MEDIA
  OTHER
}

enum LeadershipRole {
  HEAD
  DEPUTY
  COORDINATOR
  SUPERVISOR
  ASSISTANT
}

enum MemberRole {
  LEADER
  COORDINATOR
  MEMBER
  VOLUNTEER
  INTERN
}

enum MembershipStatus {
  ACTIVE
  INACTIVE
  PENDING
  PROBATION
  ALUMNI
}

// Ministry Unit model with enhanced structure
model MinistryUnit {
  id             String           @id @default(cuid())
  name           String
  description    String?
  type           MinistryUnitType
  category       MinistryCategory
  parentUnitId   String?         // For hierarchical structure
  organizationId String          // Changed from organisationId
  isActive       Boolean         @default(true)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt

  // Relations
  organization   Organization     @relation(fields: [organizationId], references: [id])
  parentUnit     MinistryUnit?   @relation("UnitHierarchy", fields: [parentUnitId], references: [id])
  childUnits     MinistryUnit[]  @relation("UnitHierarchy")
  leaders        MinistryUnitLeader[]
  members        MinistryUnitMember[]

  @@index([organizationId])
  @@index([parentUnitId])
}

model OrganizationSettings {
  id             String   @id @default(cuid())
  organizationId String   @unique
  timezone       String   @default("UTC")
  dateFormat     String   @default("DD/MM/YYYY")
  timeFormat     String   @default("HH:mm")
  organization   Organization @relation(fields: [organizationId], references: [id])
}

model Invitation {
  id             String   @id @default(cuid())
  email          String
  organizationId String
  role           Role     @default(VIEWER)
  expires        DateTime
  token          String   @unique
  createdAt      DateTime @default(now())
  used           Boolean  @default(false)

  organization   Organization @relation(fields: [organizationId], references: [id])
}

model Visitor {
  id             String   @id @default(cuid())
  firstName      String
  lastName       String
  email          String?
  phone          String?
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization   Organization @relation(fields: [organizationId], references: [id])
}

model Service {
  id             String   @id @default(cuid())
  name           String
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization   Organization @relation(fields: [organizationId], references: [id])
  attendance     Attendance[]
}

model Metric {
  id             String   @id @default(cuid())
  name           String
  value          Float
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization   Organization @relation(fields: [organizationId], references: [id])
}

model Report {
  id             String   @id @default(cuid())
  name           String
  data           Json
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization   Organization @relation(fields: [organizationId], references: [id])
}

model Attendance {
  id              String        @id @default(uuid())
  memberId        String
  serviceId       String
  classId         String?
  classSessionId  String?
  organizationId  String
  checkedInAt     DateTime      @default(now())
  checkedOutAt    DateTime?
  notes           String?
  
  // Relations
  member          Member        @relation(fields: [memberId], references: [id])
  service         Service       @relation(fields: [serviceId], references: [id])
  organization    Organization  @relation(fields: [organizationId], references: [id])
  class           Class?        @relation(fields: [classId], references: [id])
  classSession    ClassSession? @relation("ClassSessionAttendance", fields: [classSessionId], references: [id])
  session         Session?      @relation(fields: [sessionId], references: [id])
  sessionId       String?

  @@index([serviceId])
  @@index([memberId])
  @@index([organizationId])
  @@index([classId])
  @@index([classSessionId])
  @@index([sessionId])
}

model Event {
  id             String   @id @default(cuid())
  name           String
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization   Organization @relation(fields: [organizationId], references: [id])
}

model OrganizationTouchpoint {
  id             String   @id @default(cuid())
  type           String
  organizationId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  organization   Organization @relation(fields: [organizationId], references: [id])
}

model MemberClass {
  id        String   @id @default(cuid())
  memberId  String
  classId   String
  joinedAt  DateTime @default(now())
  status    String   @default("ACTIVE")

  member    Member   @relation(fields: [memberId], references: [id])
  class     Class    @relation(fields: [classId], references: [id])
}

model ClassInstructor {
  id        String   @id @default(cuid())
  classId   String
  userId    String
  createdAt DateTime @default(now())

  class     Class    @relation(fields: [classId], references: [id])
  user      User     @relation(fields: [userId], references: [id])
}

model Family {
  id        String   @id @default(cuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  members   Member[]
}

// Ministry Unit Leader model
model MinistryUnitLeader {
  id             String         @id @default(cuid())
  ministryUnitId String
  memberId       String
  role           LeadershipRole
  startDate      DateTime       @default(now())
  endDate        DateTime?
  isActive       Boolean        @default(true)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  ministryUnit   MinistryUnit   @relation(fields: [ministryUnitId], references: [id])
  member         Member         @relation(fields: [memberId], references: [id])

  @@unique([ministryUnitId, memberId, role])
  @@index([ministryUnitId])
  @@index([memberId])
}

// Ministry Unit Member model
model MinistryUnitMember {
  id             String           @id @default(cuid())
  ministryUnitId String
  memberId       String
  role           MemberRole
  status         MembershipStatus
  joinDate       DateTime         @default(now())
  leaveDate      DateTime?
  notes          String?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  // Relations
  ministryUnit   MinistryUnit     @relation(fields: [ministryUnitId], references: [id])
  member         Member           @relation(fields: [memberId], references: [id])

  @@unique([ministryUnitId, memberId])
  @@index([ministryUnitId])
  @@index([memberId])
}

model AuditLog {
  id          String    @id @default(cuid())
  entityType  String    // e.g., "attendance", "member"
  entityId    String
  action      String    // "CREATE", "UPDATE", "DELETE"
  userId      String
  user        User      @relation(fields: [userId], references: [id])
  changes     Json
  timestamp   DateTime  @default(now())
  metadata    Json?
  @@index([userId])
}
