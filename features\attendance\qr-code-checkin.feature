@mvp @p1 @attendance @africa
Feature: QR Code Check-in System
  As a Church Member
  I want to check in to services using QR codes
  So that I can quickly and easily record my attendance

  Background:
    Given the ChMS application is running
    And QR code check-in is enabled
    And church services are scheduled
    And I am a registered church member

  @p1 @smoke
  Scenario: Successful QR code check-in
    Given I am at the church for "Sunday Morning Worship"
    And a QR code is displayed for the service
    When I scan the QR code with my mobile device
    And I am redirected to the check-in page
    And I confirm my identity as "<PERSON>"
    And I click "Check In"
    Then I should be checked in successfully
    And I should see "Welcome to Sunday Morning Worship, <PERSON>!"
    And my attendance should be recorded with current timestamp

  @p1
  Scenario: QR code check-in for unregistered visitor
    Given I am a first-time visitor at the church
    And I scan the service QR code
    When I am redirected to the check-in page
    And I see "New Visitor Registration"
    And I enter my details:
      | Field      | Value              |
      | Name       | <PERSON>      |
      | Email      | <EMAIL>    |
      | Phone      | +1234567890        |
    And I click "Check In as Visitor"
    Then I should be checked in as a visitor
    And I should see "Welcome to our church, <PERSON>!"
    And a visitor record should be created
    And I should receive follow-up information

  @p1 @africa
  Scenario: QR code check-in with low bandwidth optimization
    Given I am using a low-bandwidth mobile connection
    And I scan the service QR code
    When the check-in page loads
    Then the page should load quickly with minimal data usage
    And images should be optimized for slow connections
    And the check-in process should work smoothly
    And I should see "Optimized for your connection"

  @p1
  Scenario: Family QR code check-in
    Given I am the head of the "Smith Family"
    And my family members are:
      | Name        | Age | Relationship |
      | Bob Smith   | 45  | Self         |
      | Mary Smith  | 42  | Spouse       |
      | Tim Smith   | 16  | Son          |
      | Lisa Smith  | 14  | Daughter     |
    When I scan the service QR code
    And I select "Family Check-in"
    And I see all my family members listed
    And I select who is present today
    And I click "Check In Family"
    Then all selected family members should be checked in
    And I should see "Smith family checked in successfully"

  @p0
  Scenario: Invalid or expired QR code
    Given I scan an expired QR code from last week's service
    When I am redirected to the check-in page
    Then I should see "This QR code has expired"
    And I should see current service options
    And I should be able to check in to today's active services
    And the expired code should not allow check-in

  @p1
  Scenario: QR code check-in without internet connection
    Given I scan a QR code while offline
    When the check-in page attempts to load
    Then I should see "You appear to be offline"
    And I should see "Your check-in will be processed when connection is restored"
    And I should be able to complete the check-in form
    And the check-in should sync when connection returns

  @p1
  Scenario: Multiple service QR codes
    Given multiple services are running simultaneously:
      | Service Name    | Location      | Time  |
      | Main Service    | Sanctuary     | 09:00 |
      | Youth Service   | Youth Hall    | 09:00 |
      | Children's Hour | Kids Room     | 09:00 |
    When I scan a QR code at the church
    Then I should see all available services
    And I should be able to select the correct service
    And I should be checked in to the selected service only

  @p1
  Scenario: QR code security validation
    Given I scan a QR code for "Sunday Morning Worship"
    When the system validates the QR code
    Then it should verify the code is authentic
    And it should check the code hasn't been tampered with
    And it should confirm the service is currently active
    And it should prevent fraudulent check-ins

  @p2
  Scenario: QR code check-in analytics
    Given I am a church administrator
    And members have been using QR code check-in
    When I view the QR code analytics dashboard
    Then I should see:
      | Metric                    | Value |
      | Total QR Scans Today      | 156   |
      | Successful Check-ins      | 142   |
      | New Visitor Registrations | 8     |
      | Failed Scans              | 6     |
    And I should see usage patterns by service and time

  @p2
  Scenario: Custom QR code generation for events
    Given I am organizing a special church event
    And I want to create a custom QR code
    When I navigate to "Generate QR Code"
    And I enter event details:
      | Field       | Value                    |
      | Event Name  | Christmas Concert        |
      | Date        | December 24, 2023        |
      | Location    | Main Sanctuary           |
    And I click "Generate QR Code"
    Then a unique QR code should be created for the event
    And I should be able to download the QR code image
    And the code should be active for the specified date

  @p2 @africa
  Scenario: Multilingual QR code check-in
    Given the church serves a multilingual community
    And I scan a service QR code
    When the check-in page loads
    Then I should see language options:
      | Language | Code |
      | English  | en   |
      | French   | fr   |
      | Pidgin   | pcm  |
    And I should be able to complete check-in in my preferred language
    And confirmation messages should be in the selected language
