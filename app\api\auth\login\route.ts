import { NextResponse } from "next/server";
import { verify } from "@node-rs/argon2";
import { prisma } from "@/lib/prisma";
import { rateLimiter } from '@/services/auth/rate-limit';
import { AccountLockoutService } from '@/services/auth/account-lockout';
import { sanitizeInput } from '@/services/security/input-sanitizer';
import { validateRecaptcha } from '@/services/security/recaptcha';
import { logger, trackError, trackEvent, performanceMonitor } from '@/lib/monitoring';
import { signIn } from 'next-auth/react';

interface LoginData {
  email: string;
  password: string;
  recaptchaToken?: string;
  rememberMe?: boolean;
}

export async function POST(req: Request) {
  const endTimer = performanceMonitor.start('login');
  const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
  
  let sanitizedData: LoginData = {
    email: "",
    password: "",
  };
  
  try {
    // Track login attempt
    trackEvent('login_started', { ip });

    // Rate limiting - stricter for auth endpoints
    const limiter = await rateLimiter.check(req);
    if (!limiter.success) {
      trackEvent('login_rate_limited', { ip });
      return NextResponse.json(
        { 
          error: 'Too many requests',
          message: 'Please try again later'
        }, 
        { status: 429 }
      );
    }

    const data = await req.json();
    sanitizedData = sanitizeInput(data);
    
    if (!sanitizedData.email || !sanitizedData.password) {
      trackEvent('login_invalid_input', { ip });
      return NextResponse.json(
        { 
          error: 'Invalid input',
          message: 'Email and password are required'
        }, 
        { status: 400 }
      );
    }

    // Check account lockout status
    const lockoutStatus = await AccountLockoutService.checkLockoutStatus(sanitizedData.email);
    
    if (lockoutStatus.isLocked) {
      trackEvent('login_account_locked', { 
        email: sanitizedData.email,
        ip,
        lockoutExpiresAt: lockoutStatus.lockoutExpiresAt
      });
      
      const remainingTime = lockoutStatus.lockoutExpiresAt 
        ? Math.ceil((lockoutStatus.lockoutExpiresAt.getTime() - Date.now()) / 1000 / 60)
        : 30;
        
      return NextResponse.json(
        { 
          error: 'Account locked',
          message: `Account is temporarily locked. Try again in ${remainingTime} minutes.`,
          lockedUntil: lockoutStatus.lockoutExpiresAt
        }, 
        { status: 423 } // 423 Locked
      );
    }

    // Progressive delay for repeated failed attempts
    if (lockoutStatus.nextAttemptDelay && lockoutStatus.nextAttemptDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, lockoutStatus.nextAttemptDelay));
    }

    // Validate reCAPTCHA if provided or if there have been failed attempts
    if (sanitizedData.recaptchaToken || lockoutStatus.remainingAttempts < 3) {
      const recaptchaValid = await validateRecaptcha(sanitizedData.recaptchaToken);
      if (!recaptchaValid) {
        trackEvent('login_recaptcha_failed', { email: sanitizedData.email, ip });
        await AccountLockoutService.recordFailedAttempt(sanitizedData.email, ip);
        return NextResponse.json(
          { 
            error: 'Invalid reCAPTCHA',
            message: 'Please complete the reCAPTCHA verification'
          }, 
          { status: 400 }
        );
      }
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { email: sanitizedData.email },
      select: {
        id: true,
        email: true,
        password: true,
        name: true,
        role: true,
        emailVerified: true,
        organizationId: true,
        failedLoginAttempts: true,
        lockedUntil: true,
      },
    });

    if (!user) {
      // Record failed attempt even for non-existent users to prevent enumeration
      await AccountLockoutService.recordFailedAttempt(sanitizedData.email, ip);
      trackEvent('login_user_not_found', { email: sanitizedData.email, ip });
      
      return NextResponse.json(
        { 
          error: 'Invalid credentials',
          message: 'Email or password is incorrect'
        }, 
        { status: 401 }
      );
    }

    // Verify password
    const passwordValid = await verify(user.password, sanitizedData.password);
    
    if (!passwordValid) {
      // Record failed attempt
      const newLockoutStatus = await AccountLockoutService.recordFailedAttempt(sanitizedData.email, ip);
      
      trackEvent('login_invalid_password', { 
        email: sanitizedData.email, 
        ip,
        remainingAttempts: newLockoutStatus.remainingAttempts,
        isLocked: newLockoutStatus.isLocked
      });
      
      let message = 'Email or password is incorrect';
      if (newLockoutStatus.remainingAttempts <= 1 && !newLockoutStatus.isLocked) {
        message += `. ${newLockoutStatus.remainingAttempts} attempt(s) remaining before account lockout.`;
      } else if (newLockoutStatus.isLocked) {
        message = 'Account has been temporarily locked due to too many failed attempts.';
      }
      
      return NextResponse.json(
        { 
          error: 'Invalid credentials',
          message,
          remainingAttempts: newLockoutStatus.remainingAttempts
        }, 
        { status: 401 }
      );
    }

    // Check if email is verified
    if (!user.emailVerified) {
      trackEvent('login_email_not_verified', { email: sanitizedData.email, ip });
      return NextResponse.json(
        { 
          error: 'Email not verified',
          message: 'Please verify your email address before logging in',
          requiresVerification: true
        }, 
        { status: 403 }
      );
    }

    // Reset failed login attempts on successful login
    await AccountLockoutService.resetFailedAttempts(sanitizedData.email);

    // Update last login timestamp
    await prisma.user.update({
      where: { id: user.id },
      data: { 
        lastLoginAt: new Date(),
        lastLoginIp: ip,
      },
    });

    // Track successful login
    trackEvent('login_successful', {
      userId: user.id,
      email: sanitizedData.email,
      ip,
      rememberMe: sanitizedData.rememberMe
    });

    const duration = endTimer();
    logger.info('Login successful', {
      userId: user.id,
      email: sanitizedData.email,
      duration,
      ip
    });

    // Return user data (NextAuth will handle session creation)
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        organizationId: user.organizationId,
      }
    });

  } catch (error) {
    trackError(error instanceof Error ? error : new Error('Unknown error'), { 
      context: 'login',
      email: sanitizedData.email,
      ip
    });
    
    logger.error('Login error', error as Error, {
      email: sanitizedData.email,
      ip
    });

    return NextResponse.json(
      { 
        error: 'Login failed',
        message: process.env.NODE_ENV === 'development' 
          ? (error instanceof Error ? error.message : 'Unknown error') 
          : 'An error occurred during login'
      },
      { status: 500 }
    );
  }
}
