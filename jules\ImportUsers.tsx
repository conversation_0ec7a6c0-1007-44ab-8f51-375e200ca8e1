import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Text,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  List,
  ListItem,
  Progress,
  Textarea,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Spinner,
  Icon,
} from "@chakra-ui/react";
import { useState, useEffect } from "react";
import { DownloadIcon, UploadIcon, EmailIcon } from "@chakra-ui/icons";
import { FaMagic } from "react-icons/fa"; // For a "magic" AI button

export interface ImportResults {
  successful: number;
  failed: number;
  errors: string[];
}

const ImportUsers = () => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [results, setResults] = useState<ImportResults | null>(null);
  const toast = useToast();

  // State for AI Welcome Message
  const [isGeneratingWelcomeMessage, setIsGeneratingWelcomeMessage] = useState(false);
  const [generatedWelcomeMessage, setGeneratedWelcomeMessage] = useState<string | null>(null);
  const [aiError, setAiError] = useState<string | null>(null);
  const { isOpen: isWelcomeModalOpen, onOpen: onWelcomeModalOpen, onClose: onWelcomeModalClose } = useDisclosure();


  useEffect(() => {
    // Reset AI message when results change (new import)
    setGeneratedWelcomeMessage(null);
    setAiError(null);
  }, [results]);


  const handleGenerateWelcomeMessage = async () => {
    if (!results || results.successful === 0) return;

    setIsGeneratingWelcomeMessage(true);
    setGeneratedWelcomeMessage(null);
    setAiError(null);

    // Placeholder for actual org name - ideally from context/session
    const organizationName = "[Your Church Name]"; 
    const prompt = `Draft a warm and welcoming email message for ${results.successful} new members who have just been added to ${organizationName}. The message should be generic enough to apply to all new members. Include placeholders like [New Member Name] if appropriate. Keep it concise and friendly.`;

    try {
      const response = await fetch("/api/claude/task", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          description: prompt,
          complexity: "low", // Simple text generation
        }),
      });
      const data = await response.json();

      if (data.success && data.data) {
        setGeneratedWelcomeMessage(data.data);
        onWelcomeModalOpen(); // Open modal to show message
      } else {
        setAiError(data.error || "Failed to generate welcome message.");
        toast({
          title: "AI Error",
          description: data.error || "Could not generate welcome message.",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setAiError(errorMessage);
      toast({
        title: "AI Request Failed",
        description: errorMessage,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsGeneratingWelcomeMessage(false);
    }
  };

  const downloadTemplate = () => {
    const csvContent =
      "email,name,role,department,phoneNumber,dateOfBirth,address\n" +
      '<EMAIL>,John Doe,MEMBER,Youth,+1234567890,1990-01-01,"123 Main St"';

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "user-import-template.csv";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;

    setIsUploading(true);
    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await fetch("/api/members/import", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        // The backend now returns a more structured error for non-200s if it's a known issue
        // e.g. { error: "message" } or { successful, failed, errors: ["message"] } for 400 bad CSV header
        // For a generic server error (500), data.error might be the best bet.
        // If data.errors is an array, it implies a partial success/failure that the backend handled.
        let errorMessage = "Import failed";
        if (data.error) {
            errorMessage = data.error;
        } else if (data.errors && Array.isArray(data.errors) && data.errors.length > 0) {
            errorMessage = data.errors.join('; ');
        }
        throw new Error(errorMessage);
      }

      setResults(data); // data should be { successful, failed, errors }
      
      // Customize toast based on outcome
      if (data.successful > 0 && data.failed === 0) {
        toast({
          title: "Import Complete",
          description: `Successfully imported ${data.successful} users.`,
          status: "success",
          duration: 5000,
          isClosable: true,
        });
      } else if (data.successful > 0 && data.failed > 0) {
        toast({
          title: "Import Partially Successful",
          description: `Imported ${data.successful} users. ${data.failed} failed. Check error messages.`,
          status: "warning",
          duration: 7000,
          isClosable: true,
        });
      } else if (data.failed > 0 && data.successful === 0) {
         toast({
          title: "Import Failed",
          description: `Could not import any users. ${data.errors.length > 0 ? data.errors[0] : 'Please check the file and try again.'}`,
          status: "error",
          duration: 7000,
          isClosable: true,
        });
      } else { // No successes, no failures, but maybe general errors like header mismatch
         toast({
          title: "Import Issue",
          description: data.errors && data.errors.length > 0 ? data.errors[0] : "An issue occurred during import.",
          status: "warning",
          duration: 7000,
          isClosable: true,
        });
      }

    } catch (error) {
      toast({
        title: "Import Failed",
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Box p={4} data-testid="import-users-component">
      <VStack spacing={6} align="stretch">
        <Button
          leftIcon={<DownloadIcon />}
          onClick={downloadTemplate}
          colorScheme="blue"
          variant="outline"
          data-testid="download-template-button"
        >
          Download Template
        </Button>

        <form onSubmit={handleSubmit}>
          <VStack spacing={4}>
            <FormControl>
              <FormLabel>Upload CSV File</FormLabel>
              <Input
                type="file"
                accept=".csv"
                onChange={handleFileChange}
                p={1}
                data-testid="file-input"
              />
            </FormControl>

            <Button
              type="submit"
              colorScheme="purple"
              isLoading={isUploading}
              leftIcon={<UploadIcon />}
              isDisabled={!file}
              data-testid="import-button"
            >
              Import Users
            </Button>
          </VStack>
        </form>

        {isUploading && (
          <Box>
            <Text mb={2}>Uploading...</Text>
            <Progress size="xs" isIndeterminate />
          </Box>
        )}

        {results && (
          <Alert
            status={results.failed > 0 ? "warning" : "success"}
            variant="subtle"
            flexDirection="column"
            alignItems="flex-start"
            gap={2}
          >
            <AlertIcon />
            <AlertTitle>Import Results</AlertTitle>
            <AlertDescription>
              <Text>Successfully imported: {results.successful}</Text>
              <Text>Failed: {results.failed}</Text>

              {results.successful > 0 && (
                <Button
                  mt={4}
                  leftIcon={<Icon as={FaMagic} />}
                  colorScheme="teal"
                  onClick={handleGenerateWelcomeMessage}
                  isLoading={isGeneratingWelcomeMessage}
                  loadingText="Drafting..."
                >
                  Draft Welcome Message with AI
                </Button>
              )}

              {results.errors.length > 0 && (
                <Box mt={4}>
                  <Text fontWeight="bold">Errors:</Text>
                  <List styleType="disc" pl={4}>
                    {results.errors.map((error, index) => (
                      <ListItem key={index} fontSize="sm">
                        {error}
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </AlertDescription>
          </Alert>
        )}

        {aiError && !isGeneratingWelcomeMessage && (
          <Alert status="error" mt={4}>
            <AlertIcon />
            <AlertTitle>AI Assistant Error:</AlertTitle>
            <AlertDescription>{aiError}</AlertDescription>
          </Alert>
        )}
      </VStack>

      {/* Modal to display generated welcome message */}
      <Modal isOpen={isWelcomeModalOpen} onClose={onWelcomeModalClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Draft Welcome Message</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {isGeneratingWelcomeMessage && <Spinner />}
            {generatedWelcomeMessage && (
              <Textarea
                value={generatedWelcomeMessage}
                isReadOnly
                minH="200px"
                whiteSpace="pre-wrap"
              />
            )}
            <Text fontSize="sm" color="gray.500" mt={2}>
              This message was generated by AI. Please review and edit as needed before sending.
            </Text>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={onWelcomeModalClose}>
              Close
            </Button>
            <Button variant="ghost" onClick={() => {
              if (generatedWelcomeMessage) navigator.clipboard.writeText(generatedWelcomeMessage);
              toast({ title: "Copied to clipboard!", status: "success", duration: 2000 });
            }}>
              Copy Text
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default ImportUsers;
