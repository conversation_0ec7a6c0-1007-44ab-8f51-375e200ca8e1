import csv
import subprocess
import time

PROJECT_ID = "PVT_kwDODRQRUM4A9LK-"
CSV_FILE = "pm/scripts/github-projects-import.csv"
AUTHOR_FIELD_ID = "PASTE_YOUR_AUTHOR_FIELD_ID_HERE"  # See below for how to get this

def run_gh_api(query, variables):
    result = subprocess.run(
        ["gh", "api", "graphql", "-f", f"query={query}", *[f"-f {k}={v}" for k, v in variables.items()]],
        capture_output=True, text=True
    )
    return result.stdout

def add_draft_issue(title, body):
    query = '''
    mutation($projectId:ID!, $title:String!, $body:String!) {
      addProjectV2DraftIssue(input: {
        projectId: $projectId
        title: $title
        body: $body
      }) {
        projectItem { id }
      }
    }
    '''
    variables = {"projectId": PROJECT_ID, "title": title, "body": body}
    output = run_gh_api(query, variables)
    # Extract the item ID from the output (simple parsing)
    import json
    data = json.loads(output)
    return data["data"]["addProjectV2DraftIssue"]["projectItem"]["id"]

def set_author_field(item_id, author):
    query = '''
    mutation($projectId:ID!, $itemId:ID!, $fieldId:ID!, $value:String!) {
      updateProjectV2ItemFieldValue(input: {
        projectId: $projectId
        itemId: $itemId
        fieldId: $fieldId
        value: { text: $value }
      }) {
        projectV2Item { id }
      }
    }
    '''
    variables = {
        "projectId": PROJECT_ID,
        "itemId": item_id,
        "fieldId": AUTHOR_FIELD_ID,
        "value": author
    }
    run_gh_api(query, variables)

with open(CSV_FILE, newline='', encoding='utf-8') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        if row.get("Status", "").strip().lower() == "done":
            continue
        title = row["Title"]
        notes = row.get("Notes", "")
        author = row.get("Author", "")
        print(f"Adding: {title} (Author: {author})")
        item_id = add_draft_issue(title, notes)
        if author:
            set_author_field(item_id, author)
        time.sleep(1)  # Avoid hitting rate limits

print("All items added to GitHub Project!")