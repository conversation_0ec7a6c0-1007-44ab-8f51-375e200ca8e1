# Project Documentation (`docs/`)

This directory contains documentation related to the **built ChMS application** and the **development process**.

## Contents

- **/guides:** Step-by-step guides for using specific features (e.g., `guide-attendance-system.md`).
- **/standards:** Documents defining coding, testing, and documentation standards for the project.
- **/tutorials:** Learning materials and tutorials related to the technologies or specific implementations within the project.
- **/archive:** Archived documentation that is no longer actively maintained but kept for reference.
- `api-documentation.md`: Details about the application's API endpoints.
- `user-guide.md`: General instructions for end-users on how to operate the ChMS application.
- `README.md`: (This file) Overview of the documentation directory structure.

## Related Documentation

- **Project Management ([pm/](../pm/)):** Contains planning documents like the [PRD](../pm/prd.md), [User Stories](../pm/user-stories.md), [Technical Architecture](../pm/technical-architecture.md), etc.
- **[CONTRIBUTING.md](../CONTRIBUTING.md):** Guidelines for setting up the development environment and contributing to the project.
- **[README.md](../README.md):** Main project overview and quick start guide.

## Document Versioning

All documents in this directory follow semantic versioning (MAJOR.MINOR.PATCH):

- **MAJOR:** Significant changes that fundamentally alter the document's purpose or structure
- **MINOR:** New sections or substantial updates to existing content
- **PATCH:** Minor updates, corrections, or clarifications

Each document should include a version history section at the end to track changes.

# ChMS Documentation Index

## Application Guides

- [User Guide](./user-guide.md) - End-user instructions for the ChMS application
- [Guide: Attendance System](./guide-attendance-system.md) - How to use the attendance tracking features
- [Guide: Components](./guide-components.md) - Overview of available UI components
- [Guide: Organization](./guide-organization.md) - Managing organizational structure
- [Guide: Visitor and Event](./guide-visitor-and-event.md) - Visitor registration and event management

## API Documentation

- [API Documentation](./api-documentation.md) - Details about the application's API endpoints

## Development Standards

- [Development Standards](./standards/development-standards.md) - Coding standards and best practices
- [Documentation Standards](./standards/documentation-standards.md) - How to document code and features
- [Testing Standards](./standards/testing-standards.md) - Standards for writing and maintaining tests

## Tutorials

- [Component Documentation](./tutorials/component-documentation.md)
- [Component Library and Domains](./tutorials/component-library-and-domains.md)
- [Design System Handoff](./tutorials/design-system-handoff.md)
- [Essential Files](./tutorials/essential-files.md)
- [React Hooks](./tutorials/react-hooks.md)
- [Services Architecture](./tutorials/services-architecture.md)
- [Testing Strategy](./tutorials/testing-strategy.md)
- [TypeScript Types Tutorial](./tutorials/typescript-types-tutorial.md)

## Project Management (in pm/ directory)

- [Product Requirements Document](../pm/prd.md) - Project goals, scope, and high-level features
- [Technical Architecture](../pm/technical-architecture.md) - System architecture and component design
- [User Stories](../pm/user-stories.md) - User stories derived from the PRD
- [Feature Requirements: Attendance](../pm/features/attendance-frd.md) - Detailed requirements for attendance feature
- [Testing Strategy](../pm/testing-strategy.md) - Overall testing approach and methodology
- [Security Policy](../pm/security-policy.md) - Security policies and procedures

## Contributing

- [Contributing Guidelines](../CONTRIBUTING.md) - How to contribute to the project
- [Development Environment Setup](../CONTRIBUTING.md#installation) - Setting up your development environment
