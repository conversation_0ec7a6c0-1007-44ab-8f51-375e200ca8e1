import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ImportUsers from '@/components/users/ImportUsers'; // Adjust path as needed
import { ChakraProvider, extendTheme } from '@chakra-ui/react'; // For Chakra UI components

// Mock Chakra UI components that might cause issues in test environment if not fully styled/themed
const mockUseToast = vi.fn();
vi.mock('@chakra-ui/react', async () => {
  const actualChakra = await vi.importActual('@chakra-ui/react');
  return {
    ...actualChakra,
    useToast: () => mockUseToast, // Mock useToast
  };
});

// Mock fetch
global.fetch = vi.fn();

const theme = extendTheme({}); // Basic theme for ChakraProvider

describe('ImportUsers Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset fetch mock before each test
    (global.fetch as vi.Mock).mockReset();
    mockUseToast.mockClear(); // Clear toast mock calls
  });

  const renderComponent = () => {
    render(
      <ChakraProvider theme={theme}>
        <ImportUsers />
      </ChakraProvider>
    );
  };

  it('renders correctly and shows download template button', () => {
    renderComponent();
    expect(screen.getByText('Download Template')).toBeInTheDocument();
    expect(screen.getByLabelText('Upload CSV File')).toBeInTheDocument();
    expect(screen.getByText('Import Users')).toBeInTheDocument();
  });

  it('download template button works', () => {
    // Mock URL.createObjectURL and window.URL.revokeObjectURL
    window.URL.createObjectURL = vi.fn(() => 'mock_url');
    window.URL.revokeObjectURL = vi.fn();
    // Mock document.createElement to track clicks for download links
    const mockLink = { href: '', download: '', click: vi.fn(), appendChild: vi.fn(), removeChild: vi.fn(), style: {} };
    vi.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
    // Mock document.body.appendChild and document.body.removeChild
    document.body.appendChild = vi.fn();
    document.body.removeChild = vi.fn();
    
    renderComponent();
    fireEvent.click(screen.getByText('Download Template'));
    
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockLink.download).toBe('user-import-template.csv');
    expect(window.URL.createObjectURL).toHaveBeenCalled();
    expect(window.URL.revokeObjectURL).toHaveBeenCalledWith('mock_url');
  });

  it('allows file selection and enables import button', async () => {
    renderComponent();
    const importButton = screen.getByText('Import Users').closest('button') as HTMLButtonElement;
    expect(importButton.disabled).toBe(true); // Initially disabled

    const fileInput = screen.getByLabelText('Upload CSV File') as HTMLInputElement;
    const file = new File(['email,name\<EMAIL>,John Doe'], 'test.csv', { type: 'text/csv' });
    
    await userEvent.upload(fileInput, file);

    expect(fileInput.files?.[0]).toBe(file);
    expect(fileInput.files?.length).toBe(1);
    await waitFor(() => expect(importButton.disabled).toBe(false)); // Should be enabled
  });

  it('handles successful import', async () => {
    (global.fetch as vi.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ successful: 1, failed: 0, errors: [] }),
    });

    renderComponent();
    const fileInput = screen.getByLabelText('Upload CSV File');
    const file = new File(['email,name\<EMAIL>,John Doe'], 'test.csv', { type: 'text/csv' });
    await userEvent.upload(fileInput, file);
    
    const importButton = screen.getByText('Import Users').closest('button');
    fireEvent.click(importButton!);

    await waitFor(() => expect(global.fetch).toHaveBeenCalledWith('/api/members/import', expect.any(Object)));
    await waitFor(() => expect(screen.getByText('Successfully imported: 1')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Failed: 0')).toBeInTheDocument());
    expect(mockUseToast).toHaveBeenCalledWith(expect.objectContaining({ status: 'success', title: 'Import Complete' }));
  });

  it('handles failed import from API', async () => {
    (global.fetch as vi.Mock).mockResolvedValueOnce({
      ok: false, // Simulate API error
      json: async () => ({ error: 'Test API error from server' }),
    });

    renderComponent();
    const fileInput = screen.getByLabelText('Upload CSV File');
    const file = new File(['email,name\<EMAIL>,John Doe'], 'test.csv', { type: 'text/csv' });
    await userEvent.upload(fileInput, file);

    fireEvent.click(screen.getByText('Import Users').closest('button')!);
    
    await waitFor(() => expect(global.fetch).toHaveBeenCalledTimes(1));
    expect(mockUseToast).toHaveBeenCalledWith(expect.objectContaining({ status: 'error', title: 'Import Failed', description: 'Test API error from server' }));
  });
  
  it('displays errors from a partially successful import', async () => {
    (global.fetch as vi.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ successful: 1, failed: 1, errors: ['Row 2: Invalid email'] }),
    });
    
    renderComponent();
    const fileInput = screen.getByLabelText('Upload CSV File');
    const file = new File(['email,name\<EMAIL>,John Doe\ninvalid,Invalid'], 'test.csv', { type: 'text/csv' });
    await userEvent.upload(fileInput, file);
    fireEvent.click(screen.getByText('Import Users').closest('button')!);

    await waitFor(() => screen.getByText('Successfully imported: 1'));
    await waitFor(() => screen.getByText('Failed: 1'));
    await waitFor(() => screen.getByText('Row 2: Invalid email'));
    expect(mockUseToast).toHaveBeenCalledWith(expect.objectContaining({ status: 'warning', title: 'Import Partially Successful' }));
  });

  // Tests for AI Welcome Message Feature
  describe('AI Welcome Message Feature', () => {
    it('does not show AI button initially or after failed import', async () => {
      renderComponent();
      expect(screen.queryByText('Draft Welcome Message with AI')).not.toBeInTheDocument();
    });

    it('shows AI button after successful import', async () => {
      (global.fetch as vi.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ successful: 1, failed: 0, errors: [] }),
      });
      renderComponent();
      const fileInput = screen.getByLabelText('Upload CSV File');
      const file = new File(['email,name\<EMAIL>,John Doe'], 'test.csv', { type: 'text/csv' });
      await userEvent.upload(fileInput, file);
      fireEvent.click(screen.getByText('Import Users').closest('button')!);
      
      await waitFor(() => expect(screen.getByText('Draft Welcome Message with AI')).toBeInTheDocument());
    });

    it('calls Claude API and displays welcome message in modal on AI button click', async () => {
      // First fetch for import
      (global.fetch as vi.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ successful: 1, failed: 0, errors: [] }),
      });
      // Second fetch for AI task
      (global.fetch as vi.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, data: 'AI says: Welcome new members!' }),
      });

      renderComponent();
      const fileInput = screen.getByLabelText('Upload CSV File');
      const file = new File(['email,name\<EMAIL>,John Doe'], 'test.csv', { type: 'text/csv' });
      await userEvent.upload(fileInput, file);
      fireEvent.click(screen.getByText('Import Users').closest('button')!);

      const aiButton = await screen.findByText('Draft Welcome Message with AI');
      fireEvent.click(aiButton);

      await waitFor(() => expect(global.fetch).toHaveBeenCalledWith('/api/claude/task', expect.any(Object)));
      await waitFor(() => expect(screen.getByText('Draft Welcome Message')).toBeInTheDocument()); // Modal Title
      // Use getByDisplayValue for textarea content
      await waitFor(() => expect(screen.getByDisplayValue('AI says: Welcome new members!')).toBeInTheDocument()); // Modal Body
    });

    it('handles AI API error when generating welcome message', async () => {
       (global.fetch as vi.Mock).mockResolvedValueOnce({ // Import success
        ok: true,
        json: async () => ({ successful: 1, failed: 0, errors: [] }),
      });
      (global.fetch as vi.Mock).mockResolvedValueOnce({ // AI task fails
        ok: true, 
        json: async () => ({ success: false, error: 'AI failed to generate message' }),
      });
      
      renderComponent();
      const fileInput = screen.getByLabelText('Upload CSV File');
      const file = new File(['email,name\<EMAIL>,John Doe'], 'test.csv', { type: 'text/csv' });
      await userEvent.upload(fileInput, file);
      fireEvent.click(screen.getByText('Import Users').closest('button')!);

      const aiButton = await screen.findByText('Draft Welcome Message with AI');
      fireEvent.click(aiButton);

      await waitFor(() => expect(screen.getByText('AI Assistant Error:')).toBeInTheDocument());
      await waitFor(() => expect(screen.getByText('AI failed to generate message')).toBeInTheDocument());
      expect(mockUseToast).toHaveBeenCalledWith(expect.objectContaining({ status: 'error', title: 'AI Error', description: 'AI failed to generate message' }));
    });
  });
});

[end of src/__tests__/components/users/ImportUsers.test.tsx]
