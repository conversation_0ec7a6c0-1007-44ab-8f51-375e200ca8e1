@mvp @p1 @reporting @analytics
Feature: Attendance Reporting and Analytics
  As a Church Administrator or Pastor
  I want to generate attendance reports and analytics
  So that I can track church growth, identify trends, and make informed ministry decisions

  Background:
    Given I am logged in as a Church Administrator or Pastor
    And I have permissions to view attendance reports
    And attendance data exists in the system
    And multiple services and events have been conducted

  @p1 @smoke
  Scenario: Generate basic attendance report
    Given attendance data exists for the past month
    When I navigate to "Reports" > "Attendance Reports"
    And I select report parameters:
      | Parameter    | Value                    |
      | Date Range   | Last 30 days             |
      | Service Type | All services             |
      | Report Type  | Summary                  |
    And I click "Generate Report"
    Then I should see an attendance summary report showing:
      | Metric                | Value |
      | Total Services        | 12    |
      | Average Attendance    | 145   |
      | Highest Attendance    | 180   |
      | Lowest Attendance     | 120   |
      | Growth Rate          | +5.2% |
    And the report should be displayed in a clear, readable format

  @p1
  Scenario: Detailed service-specific attendance report
    Given I want to analyze attendance for "Sunday Morning Worship"
    When I select report parameters:
      | Parameter      | Value                    |
      | Service Name   | Sunday Morning Worship   |
      | Date Range     | Last 3 months            |
      | Detail Level   | Detailed                 |
      | Group By       | Week                     |
    And I generate the report
    Then I should see weekly attendance data:
      | Week Starting | Attendance | Change from Previous |
      | 2023-10-01   | 142        | +3                   |
      | 2023-10-08   | 156        | +14                  |
      | 2023-10-15   | 148        | -8                   |
      | 2023-10-22   | 163        | +15                  |
    And I should see trend analysis and patterns

  @p1
  Scenario: Member attendance tracking report
    Given I want to track individual member attendance
    When I navigate to "Member Attendance Reports"
    And I select parameters:
      | Parameter        | Value              |
      | Date Range       | Last 6 months      |
      | Member Category  | All members        |
      | Attendance Type  | Regular services   |
      | Sort By          | Attendance rate    |
    And I generate the report
    Then I should see member attendance data:
      | Member Name    | Total Services | Attended | Attendance Rate |
      | John Smith     | 24            | 22       | 92%            |
      | Mary Johnson   | 24            | 18       | 75%            |
      | Bob Wilson     | 24            | 12       | 50%            |
    And I should be able to identify members needing pastoral care

  @p1
  Scenario: Comparative attendance analysis
    Given I want to compare attendance across different time periods
    When I select "Comparative Analysis" report
    And I set comparison parameters:
      | Parameter           | Value                    |
      | Primary Period      | Last 3 months            |
      | Comparison Period   | Same period last year    |
      | Services           | All regular services     |
      | Metrics            | Average, Growth, Trends  |
    And I generate the comparison
    Then I should see side-by-side analysis:
      | Metric              | Current Period | Last Year | Change |
      | Average Attendance  | 152           | 138       | +10.1% |
      | Total Unique Visitors| 45           | 32        | +40.6% |
      | Regular Attendees   | 120           | 115       | +4.3%  |
    And I should see visual charts showing trends

  @p2
  Scenario: Attendance forecasting and projections
    Given I have historical attendance data for 2+ years
    When I navigate to "Attendance Forecasting"
    And I select forecasting parameters:
      | Parameter          | Value                    |
      | Historical Period  | Last 2 years             |
      | Forecast Period    | Next 6 months            |
      | Service Type       | Sunday Morning Worship   |
      | Include Seasonality| Yes                      |
    And I generate the forecast
    Then I should see projected attendance figures:
      | Month      | Projected Attendance | Confidence Level |
      | November   | 165 ± 12            | 85%             |
      | December   | 180 ± 15            | 80%             |
      | January    | 145 ± 18            | 75%             |
    And I should see factors affecting the projections

  @p1
  Scenario: First-time visitor tracking report
    Given I want to track visitor conversion and retention
    When I generate a "Visitor Analysis" report
    And I set parameters:
      | Parameter        | Value           |
      | Date Range       | Last 6 months   |
      | Visitor Type     | First-time      |
      | Track Conversion | Yes             |
    And I generate the report
    Then I should see visitor metrics:
      | Metric                    | Value |
      | Total First-time Visitors | 78    |
      | Returned for 2nd Visit    | 45    |
      | Returned for 3rd Visit    | 28    |
      | Became Regular Members    | 12    |
      | Conversion Rate          | 15.4% |
    And I should see visitor follow-up effectiveness

  @p1
  Scenario: Department and ministry attendance reports
    Given I want to analyze attendance by ministry departments
    When I select "Ministry Attendance Report"
    And I choose parameters:
      | Parameter      | Value                    |
      | Date Range     | Last quarter             |
      | Departments    | Youth, Children, Adults  |
      | Include Events | Yes                      |
    And I generate the report
    Then I should see department-specific data:
      | Department | Regular Attendance | Event Attendance | Growth Rate |
      | Youth      | 35                | 48              | +12%       |
      | Children   | 42                | 38              | +8%        |
      | Adults     | 98                | 85              | +3%        |
    And I should see ministry effectiveness metrics

  @p2
  Scenario: Custom attendance report builder
    Given I need a specific report not available in templates
    When I navigate to "Custom Report Builder"
    And I configure custom parameters:
      | Element          | Selection                    |
      | Data Sources     | Attendance, Members, Services|
      | Filters          | Age group, Membership status |
      | Grouping         | By month and service type    |
      | Calculations     | Average, percentage change   |
      | Visualizations   | Bar chart, trend line        |
    And I click "Build Report"
    Then a custom report should be generated with my specifications
    And I should be able to save the report template for future use

  @p1
  Scenario: Attendance report export and sharing
    Given I have generated an attendance report
    When I want to share the report with church leadership
    And I click "Export Report"
    And I select export options:
      | Option     | Selection |
      | Format     | PDF       |
      | Include    | Charts    |
      | Recipients | Pastor, Board Members |
    And I click "Export and Share"
    Then the report should be exported in PDF format
    And it should be automatically sent to selected recipients
    And a copy should be saved in the reports archive

  @p2
  Scenario: Automated attendance report scheduling
    Given I want regular attendance reports for church leadership
    When I navigate to "Report Scheduling"
    And I set up an automated report:
      | Parameter       | Value                    |
      | Report Type     | Weekly Attendance Summary|
      | Schedule        | Every Monday at 9 AM     |
      | Recipients      | Pastor, Administrator    |
      | Format          | PDF and Excel            |
      | Include Charts  | Yes                      |
    And I click "Schedule Report"
    Then the report should be scheduled successfully
    And it should be automatically generated and sent weekly
    And I should receive confirmation of each delivery
