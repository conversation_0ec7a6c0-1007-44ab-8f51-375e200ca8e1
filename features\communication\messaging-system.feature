@mvp @p2 @communication @africa
Feature: Church Communication and Messaging System
  As a Church Administrator or Pastor
  I want to communicate effectively with members and visitors
  So that I can keep the congregation informed, engaged, and connected

  Background:
    Given I am logged in as a Church Administrator or Pastor
    And I have permissions to send communications
    And the messaging system is configured
    And member contact preferences are set up

  @p2 @smoke @africa
  Scenario: Send announcement via multiple channels
    Given I need to announce a special service
    When I navigate to "Communications" > "Send Announcement"
    And I create a new announcement:
      | Field           | Value                                    |
      | Title           | Special Thanksgiving Service             |
      | Message         | Join us for a special thanksgiving...    |
      | Priority        | High                                     |
      | Target Audience | All active members                       |
    And I select communication channels:
      | Channel     | Selected | Reason                        |
      | Email       | Yes      | Detailed information          |
      | SMS         | Yes      | Quick notification            |
      | WhatsApp    | Yes      | Popular in Africa             |
      | Church App  | Yes      | Push notification             |
      | Website     | Yes      | Public announcement           |
    And I click "Send Announcement"
    Then the announcement should be sent via all selected channels
    And I should see delivery confirmation for each channel
    And members should receive the message according to their preferences

  @p2 @africa
  Scenario: WhatsApp group messaging for African context
    Given WhatsApp is widely used in our community
    When I set up WhatsApp group communications:
      | Group Name           | Purpose                    | Members    |
      | Church Announcements | General church updates     | All members|
      | Youth Ministry       | Youth-specific messages    | Youth      |
      | Prayer Requests      | Prayer sharing             | Volunteers |
      | Leadership Team      | Leadership communications  | Leaders    |
    And I send a message to "Church Announcements":
      | Message | "Sunday service starts at 9 AM. Please arrive early for prayer time." |
    Then the message should be sent to all group members
    And I should see delivery and read receipts
    And members should be able to respond appropriately

  @p2 @africa
  Scenario: SMS messaging with low-bandwidth optimization
    Given many members have basic phones with limited data
    When I send an SMS announcement:
      | Field           | Value                                    |
      | Message         | Church service Sun 9AM. Special guest speaker. Bring friends! |
      | Target          | All members with SMS preference          |
      | Character Count | 78 characters                           |
      | Cost Estimate   | $0.15 per message                       |
    And I optimize for low-cost delivery
    Then the SMS should be sent efficiently
    And I should see delivery confirmation
    And the message should be within character limits to minimize cost

  @p2
  Scenario: Personalized member communication
    Given I want to send personalized messages to new members
    When I create a personalized message template:
      | Template Field    | Value                                    |
      | Greeting         | Hello {{firstName}},                     |
      | Message Body     | Welcome to {{churchName}}! We're excited to have you join our {{membershipType}} family. |
      | Call to Action   | Please join us for our newcomer's class on {{nextNewcomerDate}}. |
      | Closing          | Blessings, Pastor {{pastorName}}         |
    And I send to new members:
      | Member Name    | Membership Type | Next Newcomer Date |
      | Sarah Johnson  | Regular Member  | Next Sunday        |
      | Mike Brown     | Associate       | Next Sunday        |
    Then each member should receive a personalized message
    And the template variables should be properly replaced

  @p2
  Scenario: Emergency communication system
    Given there is an urgent situation affecting church services
    When I activate emergency communication:
      | Field           | Value                                    |
      | Emergency Type  | Service Cancellation                     |
      | Urgency Level   | High                                     |
      | Message         | URGENT: Sunday service cancelled due to power outage. Online service at 9 AM on Facebook Live. |
      | Send To         | All members and regular visitors         |
      | Channels        | SMS, WhatsApp, Email, Push Notification |
    And I click "Send Emergency Alert"
    Then the message should be sent immediately via all channels
    And I should see real-time delivery status
    And members should receive the alert within 5 minutes

  @p2
  Scenario: Communication preference management
    Given members have different communication preferences
    When I view member communication settings:
      | Member Name    | Email | SMS | WhatsApp | Phone | Mail |
      | John Smith     | Yes   | Yes | No       | No    | No   |
      | Mary Johnson   | Yes   | No  | Yes      | Yes   | No   |
      | Bob Wilson     | No    | Yes | Yes      | No    | Yes  |
    And I send a general announcement
    Then each member should receive the message via their preferred channels only
    And no member should receive messages via channels they've opted out of
    And I should see delivery statistics by channel

  @p2
  Scenario: Automated follow-up communication sequences
    Given I want to set up automated follow-up for new visitors
    When I create a communication sequence:
      | Day | Message Type | Content                                    | Channel |
      | 1   | Welcome      | Thank you for visiting us today!           | Email   |
      | 3   | Information  | Learn more about our ministries            | Email   |
      | 7   | Invitation   | Join us again this Sunday                  | SMS     |
      | 14  | Personal     | Pastor would like to connect with you      | Phone   |
    And a new visitor "Jennifer Davis" is registered
    Then the automated sequence should be triggered
    And Jennifer should receive messages according to the schedule
    And I should be able to track the sequence progress

  @p2
  Scenario: Multilingual communication support
    Given our congregation speaks multiple languages
    When I create a multilingual announcement:
      | Language | Message                                    |
      | English  | Sunday service at 9 AM in the main hall   |
      | French   | Service dominical à 9h dans la salle principale |
      | Pidgin   | Church service for 9 o'clock for main hall |
    And I send to members based on their language preference:
      | Member Name    | Preferred Language |
      | John Smith     | English           |
      | Marie Dubois   | French            |
      | Emeka Okafor   | Pidgin            |
    Then each member should receive the message in their preferred language
    And the system should track delivery by language

  @p2
  Scenario: Communication analytics and effectiveness
    Given I have sent multiple communications over time
    When I view communication analytics:
      | Metric              | Email | SMS  | WhatsApp | Phone |
      | Messages Sent       | 1,250 | 890  | 1,100    | 45    |
      | Delivery Rate       | 95%   | 98%  | 97%      | 89%   |
      | Open/Read Rate      | 65%   | 85%  | 92%      | 100%  |
      | Response Rate       | 12%   | 8%   | 25%      | 67%   |
      | Cost per Message    | $0.02 | $0.05| $0.01    | $0.50 |
    Then I should see which channels are most effective
    And I should see recommendations for optimizing communication strategy
    And I should be able to adjust future communications based on analytics

  @p2
  Scenario: Prayer request communication system
    Given members want to share and receive prayer requests
    When I set up a prayer request system:
      | Feature              | Setting                           |
      | Submission Method    | Online form, SMS, WhatsApp        |
      | Review Process       | Pastor approval required          |
      | Distribution         | Weekly prayer list                |
      | Privacy Levels       | Public, Members only, Leadership  |
      | Follow-up           | Automatic praise report request   |
    And a member submits a prayer request:
      | Field           | Value                                    |
      | Requester       | Sarah Johnson                            |
      | Request         | Pray for my job interview next week      |
      | Privacy Level   | Members only                             |
      | Urgency         | Normal                                   |
    Then the request should go through the approval process
    And approved requests should be included in the weekly prayer list
    And the requester should receive confirmation and follow-up
