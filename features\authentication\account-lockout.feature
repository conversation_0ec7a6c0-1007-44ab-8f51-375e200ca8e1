@mvp @p0 @authentication
Feature: Account Lockout Security System
  As a System Administrator
  I want accounts to be locked after multiple failed login attempts
  So that the system is protected from brute force attacks

  Background:
    Given the ChMS application is running
    And the account lockout system is enabled
    And user accounts exist in the system
    And lockout settings are configured:
      | max_attempts | lockout_duration | progressive_delay |
      | 5           | 15 minutes       | enabled          |

  @p0 @smoke
  Scenario: Account lockout after maximum failed attempts
    Given I am on the login page
    And the user "<EMAIL>" has no previous failed attempts
    When I attempt to login with "<EMAIL>" and wrong password 5 times
    Then the account should be locked
    And I should see the message "Account locked due to multiple failed attempts"
    And the lockout should last for 15 minutes
    And login attempts should be blocked during lockout period

  @p0
  Scenario: Progressive delay between failed attempts
    Given I am on the login page
    And the user "<EMAIL>" has no previous failed attempts
    When I fail to login with "<EMAIL>" for the 1st time
    Then I should wait 1 second before next attempt
    When I fail to login with "<EMAIL>" for the 2nd time
    Then I should wait 2 seconds before next attempt
    When I fail to login with "<EMAIL>" for the 3rd time
    Then I should wait 4 seconds before next attempt
    When I fail to login with "<EMAIL>" for the 4th time
    Then I should wait 8 seconds before next attempt

  @p0
  Scenario: Successful login resets failed attempt counter
    Given the user "<EMAIL>" has 3 failed login attempts
    When I login successfully with "<EMAIL>" and correct password
    Then the failed attempt counter should be reset to 0
    And no lockout should be applied
    And I should be logged in normally

  @p0
  Scenario: Account unlock after lockout period expires
    Given the user "<EMAIL>" is locked out
    And the lockout was applied 16 minutes ago
    When I attempt to login with "<EMAIL>" and correct password
    Then the account should be unlocked automatically
    And I should be logged in successfully
    And the failed attempt counter should be reset

  @p0
  Scenario: Login attempts blocked during lockout period
    Given the user "<EMAIL>" is currently locked out
    And 5 minutes have passed since lockout
    When I attempt to login with "<EMAIL>" and correct password
    Then the login should be blocked
    And I should see "Account locked. Try again in 10 minutes"
    And the lockout timer should not be reset

  @p1
  Scenario: Different users have independent lockout counters
    Given user "<EMAIL>" has 4 failed attempts
    And user "<EMAIL>" has 2 failed attempts
    When "<EMAIL>" fails login attempt #5
    Then only "<EMAIL>" should be locked out
    And "<EMAIL>" should still be able to login
    And "<EMAIL>" should have 2 failed attempts remaining

  @p1
  Scenario: Admin can manually unlock accounts
    Given the user "<EMAIL>" is locked out
    And I am logged in as an administrator
    When I navigate to the user management page
    And I select "<EMAIL>"
    And I click "Unlock Account"
    Then the account should be unlocked immediately
    And the failed attempt counter should be reset
    And the user should be able to login normally

  @p1
  Scenario: Lockout notification to administrators
    Given administrator notifications are enabled
    When a user account "<EMAIL>" gets locked out
    Then an alert should be sent to system administrators
    And the alert should include:
      | user_email        | lockout_time | failed_attempts | ip_address |
      | <EMAIL>   | current_time | 5              | *********** |

  @p1
  Scenario: Lockout audit logging
    Given audit logging is enabled
    When the user "<EMAIL>" gets locked out
    Then a security event should be logged with:
      | event_type | user_email      | timestamp    | ip_address  | attempts |
      | LOCKOUT   | <EMAIL> | current_time | *********** | 5       |
    And when the account is unlocked
    Then an unlock event should be logged

  @p2
  Scenario: IP-based lockout protection
    Given IP-based lockout is enabled
    When multiple failed attempts come from IP "*************"
    And the attempts exceed the IP threshold of 20 per hour
    Then the IP address should be temporarily blocked
    And all login attempts from that IP should be rejected
    And legitimate users from other IPs should not be affected
