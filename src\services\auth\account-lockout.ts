import { PrismaClient } from '@prisma/client';
import { SECURITY_CONSTANTS } from '@/config/security';
import { logger } from '@/lib/logger';
import { prisma } from '@/lib/prisma';

export interface LockoutStatus {
  isLocked: boolean;
  remainingAttempts: number;
  lockoutExpiresAt?: Date;
  nextAttemptDelay?: number;
}

export class AccountLockoutService {
  /**
   * Check if an account is currently locked
   */
  static async checkLockoutStatus(email: string): Promise<LockoutStatus> {
    try {
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          failedLoginAttempts: true,
          lastFailedLoginAt: true,
          lockedUntil: true,
        },
      });

      if (!user) {
        return {
          isLocked: false,
          remainingAttempts: SECURITY_CONSTANTS.ACCOUNT_LOCKOUT.MAX_FAILED_ATTEMPTS,
        };
      }

      const now = new Date();
      const { failedLoginAttempts, lockedUntil } = user;

      // Check if account is currently locked
      if (lockedUntil && lockedUntil > now) {
        return {
          isLocked: true,
          remainingAttempts: 0,
          lockoutExpiresAt: lockedUntil,
        };
      }

      // Calculate remaining attempts
      const remainingAttempts = Math.max(
        0,
        SECURITY_CONSTANTS.ACCOUNT_LOCKOUT.MAX_FAILED_ATTEMPTS - (failedLoginAttempts || 0)
      );

      // Calculate progressive delay if enabled
      let nextAttemptDelay = 0;
      if (SECURITY_CONSTANTS.ACCOUNT_LOCKOUT.PROGRESSIVE_DELAY && failedLoginAttempts > 0) {
        // Exponential backoff: 2^attempts seconds (max 60 seconds)
        nextAttemptDelay = Math.min(Math.pow(2, failedLoginAttempts) * 1000, 60000);
      }

      return {
        isLocked: false,
        remainingAttempts,
        nextAttemptDelay,
      };
    } catch (error) {
      logger.error('Error checking lockout status', error as Error, { email });
      // Fail secure - assume account is locked if we can't check
      return {
        isLocked: true,
        remainingAttempts: 0,
      };
    }
  }

  /**
   * Record a failed login attempt
   */
  static async recordFailedAttempt(email: string, ip?: string): Promise<LockoutStatus> {
    try {
      const user = await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          failedLoginAttempts: true,
          lockedUntil: true,
        },
      });

      if (!user) {
        // Don't reveal if user exists or not
        return {
          isLocked: false,
          remainingAttempts: SECURITY_CONSTANTS.ACCOUNT_LOCKOUT.MAX_FAILED_ATTEMPTS - 1,
        };
      }

      const now = new Date();
      const currentAttempts = (user.failedLoginAttempts || 0) + 1;
      const maxAttempts = SECURITY_CONSTANTS.ACCOUNT_LOCKOUT.MAX_FAILED_ATTEMPTS;

      let updateData: any = {
        failedLoginAttempts: currentAttempts,
        lastFailedLoginAt: now,
      };

      // Lock account if max attempts reached
      if (currentAttempts >= maxAttempts) {
        const lockoutDuration = SECURITY_CONSTANTS.ACCOUNT_LOCKOUT.LOCKOUT_DURATION;
        updateData.lockedUntil = new Date(now.getTime() + lockoutDuration);
        
        logger.warn('Account locked due to failed login attempts', {
          email,
          attempts: currentAttempts,
          ip,
          lockedUntil: updateData.lockedUntil,
        });
      }

      await prisma.user.update({
        where: { id: user.id },
        data: updateData,
      });

      const remainingAttempts = Math.max(0, maxAttempts - currentAttempts);
      
      return {
        isLocked: currentAttempts >= maxAttempts,
        remainingAttempts,
        lockoutExpiresAt: updateData.lockedUntil,
      };
    } catch (error) {
      logger.error('Error recording failed login attempt', error as Error, { email, ip });
      throw error;
    }
  }

  /**
   * Reset failed login attempts after successful login
   */
  static async resetFailedAttempts(email: string): Promise<void> {
    try {
      await prisma.user.updateMany({
        where: { 
          email,
          OR: [
            { failedLoginAttempts: { gt: 0 } },
            { lockedUntil: { not: null } },
          ],
        },
        data: {
          failedLoginAttempts: 0,
          lastFailedLoginAt: null,
          lockedUntil: null,
        },
      });

      logger.info('Reset failed login attempts', { email });
    } catch (error) {
      logger.error('Error resetting failed login attempts', error as Error, { email });
      // Don't throw - this is not critical for login flow
    }
  }

  /**
   * Manually unlock an account (admin function)
   */
  static async unlockAccount(email: string, adminEmail: string): Promise<void> {
    try {
      const result = await prisma.user.updateMany({
        where: { email },
        data: {
          failedLoginAttempts: 0,
          lastFailedLoginAt: null,
          lockedUntil: null,
        },
      });

      if (result.count > 0) {
        logger.info('Account manually unlocked', { 
          email, 
          unlockedBy: adminEmail,
        });
      }
    } catch (error) {
      logger.error('Error unlocking account', error as Error, { email, adminEmail });
      throw error;
    }
  }

  /**
   * Get lockout statistics for monitoring
   */
  static async getLockoutStats(timeframe: 'hour' | 'day' | 'week' = 'day') {
    try {
      const timeMap = {
        hour: 60 * 60 * 1000,
        day: 24 * 60 * 60 * 1000,
        week: 7 * 24 * 60 * 60 * 1000,
      };

      const since = new Date(Date.now() - timeMap[timeframe]);

      const [lockedAccounts, failedAttempts] = await Promise.all([
        prisma.user.count({
          where: {
            lockedUntil: { gt: new Date() },
          },
        }),
        prisma.user.count({
          where: {
            lastFailedLoginAt: { gte: since },
            failedLoginAttempts: { gt: 0 },
          },
        }),
      ]);

      return {
        currentlyLocked: lockedAccounts,
        recentFailedAttempts: failedAttempts,
        timeframe,
      };
    } catch (error) {
      logger.error('Error getting lockout stats', error as Error);
      return {
        currentlyLocked: 0,
        recentFailedAttempts: 0,
        timeframe,
      };
    }
  }
}
