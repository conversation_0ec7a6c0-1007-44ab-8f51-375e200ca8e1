import { setWorldConstructor, World, IWorldOptions } from '@cucumber/cucumber';
import { <PERSON>rowser, Page, chromium, firefox, webkit } from 'playwright';
import { render, RenderResult } from '@testing-library/react';
import { ReactElement } from 'react';
import { <PERSON><PERSON>Provider } from '@chakra-ui/react';
import { theme } from '@/theme';
import { prismaMock } from '../../mocks/prisma';
import { vi } from 'vitest';

// World parameters interface
interface WorldParameters {
  baseUrl: string;
  timeout: number;
  headless: boolean;
  slowMo?: number;
}

// Custom World class that extends Cucumber's World
export class CustomWorld extends World<WorldParameters> {
  // Browser automation
  public browser?: Browser;
  public page?: Page;
  
  // React Testing Library
  public renderResult?: RenderResult;
  public lastRenderedComponent?: ReactElement;
  
  // Test data and state
  public testData: Record<string, any> = {};
  public currentUser?: any;
  public apiResponses: Record<string, any> = {};
  
  // Database mock
  public db = prismaMock;
  
  constructor(options: IWorldOptions<WorldParameters>) {
    super(options);
  }

  // Browser setup methods
  async setupBrowser(browserType: 'chromium' | 'firefox' | 'webkit' = 'chromium') {
    const browsers = { chromium, firefox, webkit };
    this.browser = await browsers[browserType].launch({
      headless: this.parameters.headless,
      slowMo: this.parameters.slowMo || 0,
    });
    
    this.page = await this.browser.newPage();
    await this.page.setDefaultTimeout(this.parameters.timeout);
    
    // Set viewport for consistent testing
    await this.page.setViewportSize({ width: 1280, height: 720 });
  }

  async closeBrowser() {
    if (this.page) {
      await this.page.close();
      this.page = undefined;
    }
    if (this.browser) {
      await this.browser.close();
      this.browser = undefined;
    }
  }

  // Navigation helpers
  async navigateTo(path: string) {
    if (!this.page) {
      throw new Error('Browser not initialized. Call setupBrowser() first.');
    }
    const url = `${this.parameters.baseUrl}${path}`;
    await this.page.goto(url);
  }

  async waitForPageLoad() {
    if (!this.page) return;
    await this.page.waitForLoadState('networkidle');
  }

  // React component testing helpers
  renderComponent(component: ReactElement) {
    this.lastRenderedComponent = component;
    this.renderResult = render(
      <ChakraProvider theme={theme}>
        {component}
      </ChakraProvider>
    );
    return this.renderResult;
  }

  // Authentication helpers
  async loginAs(userType: 'admin' | 'pastor' | 'member' | 'visitor') {
    const users = {
      admin: {
        email: '<EMAIL>',
        password: 'admin123',
        role: 'ADMIN',
        name: 'Church Administrator'
      },
      pastor: {
        email: '<EMAIL>',
        password: 'pastor123',
        role: 'PASTOR',
        name: 'Pastor John'
      },
      member: {
        email: '<EMAIL>',
        password: 'member123',
        role: 'MEMBER',
        name: 'John Smith'
      },
      visitor: {
        email: '<EMAIL>',
        password: 'visitor123',
        role: 'VISITOR',
        name: 'Jane Visitor'
      }
    };

    this.currentUser = users[userType];
    
    if (this.page) {
      // Browser-based login
      await this.page.goto(`${this.parameters.baseUrl}/login`);
      await this.page.fill('[data-testid="email"]', this.currentUser.email);
      await this.page.fill('[data-testid="password"]', this.currentUser.password);
      await this.page.click('[data-testid="submit"]');
      await this.waitForPageLoad();
    }
  }

  // Database helpers
  async seedDatabase(data: Record<string, any[]>) {
    // Mock database seeding for tests
    Object.entries(data).forEach(([table, records]) => {
      this.testData[table] = records;
    });
  }

  async clearDatabase() {
    this.testData = {};
    // Reset Prisma mocks
    vi.clearAllMocks();
  }

  // API helpers
  async mockApiResponse(endpoint: string, response: any, status: number = 200) {
    this.apiResponses[endpoint] = { response, status };
  }

  // Utility methods
  async takeScreenshot(name: string) {
    if (this.page) {
      await this.page.screenshot({ 
        path: `reports/cucumber/screenshots/${name}.png`,
        fullPage: true 
      });
    }
  }

  async waitFor(milliseconds: number) {
    await new Promise(resolve => setTimeout(resolve, milliseconds));
  }

  // Form helpers
  async fillForm(formData: Record<string, string>) {
    if (!this.page) return;
    
    for (const [field, value] of Object.entries(formData)) {
      const selector = `[data-testid="${field}"], [name="${field}"], #${field}`;
      await this.page.fill(selector, value);
    }
  }

  async submitForm(formSelector: string = 'form') {
    if (!this.page) return;
    await this.page.click(`${formSelector} [type="submit"], ${formSelector} button[type="submit"]`);
  }

  // Table helpers
  async getTableData(tableSelector: string = '[data-testid="data-table"]') {
    if (!this.page) return [];
    
    const rows = await this.page.$$(`${tableSelector} tbody tr`);
    const data = [];
    
    for (const row of rows) {
      const cells = await row.$$('td');
      const rowData = [];
      for (const cell of cells) {
        rowData.push(await cell.textContent());
      }
      data.push(rowData);
    }
    
    return data;
  }

  // Cleanup method
  async cleanup() {
    await this.closeBrowser();
    this.testData = {};
    this.currentUser = undefined;
    this.apiResponses = {};
    this.renderResult = undefined;
    this.lastRenderedComponent = undefined;
  }
}

// Set the custom world constructor
setWorldConstructor(CustomWorld);
