@mvp @p0 @organization
Feature: Organization Setup and Management
  As a Church Administrator
  I want to set up and manage my church organization
  So that I can configure the system for my church's specific needs and structure

  Background:
    Given I am logged in as a System Administrator
    And I have permissions to manage organization settings
    And the organization management system is available

  @p0 @smoke
  Scenario: Initial organization creation
    Given I am setting up ChMS for the first time
    When I navigate to "Organization Setup"
    And I enter organization details:
      | Field           | Value                        |
      | Organization    | Grace Community Church       |
      | Address         | 123 Church Street            |
      | City            | Lagos                        |
      | Country         | Nigeria                      |
      | Phone           | +234-************           |
      | Email           | <EMAIL>         |
      | Website         | www.gracechurch.ng           |
      | Timezone        | Africa/Lagos                 |
    And I click "Create Organization"
    Then the organization should be created successfully
    And I should see "Grace Community Church setup completed"
    And I should be assigned as the organization administrator
    And the system should be configured with default settings

  @p0
  Scenario: Organization profile management
    Given my organization "Grace Community Church" is already set up
    When I navigate to "Organization Settings"
    And I update the organization information:
      | Field           | New Value                    |
      | Phone           | +234-************           |
      | Website         | www.newgracechurch.ng        |
      | Description     | A vibrant community church   |
    And I click "Save Changes"
    Then the organization profile should be updated
    And I should see "Organization profile updated successfully"
    And the changes should be reflected throughout the system

  @p1
  Scenario: Multi-location church setup
    Given I am managing a church with multiple locations
    When I navigate to "Locations Management"
    And I add a new location:
      | Field           | Value                        |
      | Location Name   | Grace Church - Victoria Island|
      | Address         | 456 Victoria Street          |
      | City            | Lagos                        |
      | Contact Person  | Pastor John Smith            |
      | Phone           | +234-************           |
    And I click "Add Location"
    Then the new location should be added successfully
    And I should see "Location added: Grace Church - Victoria Island"
    And the location should be available for service scheduling

  @p1
  Scenario: Department and ministry setup
    Given my organization is set up
    When I navigate to "Departments & Ministries"
    And I create departments:
      | Department Name | Description              | Head                |
      | Youth Ministry  | Young adults and teens   | Pastor Sarah Jones  |
      | Children's Min  | Kids programs            | Sister Mary Brown   |
      | Music Ministry  | Worship and choir        | Brother David Lee   |
      | Outreach       | Community outreach       | Deacon Mike Wilson  |
    And I click "Save Departments"
    Then all departments should be created successfully
    And each department should have its assigned head
    And departments should be available for member assignment

  @p0
  Scenario: Service schedule configuration
    Given my organization is set up
    When I navigate to "Service Schedule"
    And I configure regular services:
      | Service Name           | Day      | Time  | Location    | Duration |
      | Sunday Morning Worship | Sunday   | 09:00 | Main Hall   | 120 min  |
      | Sunday School          | Sunday   | 10:30 | Classrooms  | 60 min   |
      | Wednesday Bible Study  | Wednesday| 19:00 | Main Hall   | 90 min   |
      | Friday Night Service   | Friday   | 19:30 | Main Hall   | 90 min   |
    And I click "Save Schedule"
    Then the service schedule should be configured
    And services should be available for attendance tracking
    And members should be able to see the schedule

  @p1
  Scenario: Custom member categories setup
    Given my organization is set up
    When I navigate to "Member Categories"
    And I create custom categories:
      | Category Name    | Description                    | Permissions        |
      | Full Members     | Baptized church members        | All access         |
      | Associate Members| Regular attendees              | Limited access     |
      | Visitors         | First-time and occasional      | Basic access       |
      | Staff            | Church employees               | Administrative     |
      | Volunteers       | Ministry volunteers            | Ministry access    |
      | Youth            | Young members under 18         | Youth programs     |
    And I click "Save Categories"
    Then all member categories should be created
    And categories should be available for member assignment
    And appropriate permissions should be applied

  @p1
  Scenario: Organization branding and customization
    Given my organization is set up
    When I navigate to "Branding & Customization"
    And I upload organization logo
    And I set brand colors:
      | Element         | Color   |
      | Primary Color   | #2B6CB0 |
      | Secondary Color | #4A5568 |
      | Accent Color    | #38A169 |
    And I customize welcome messages:
      | Message Type    | Content                                    |
      | New Member      | Welcome to Grace Community Church family! |
      | Visitor         | Thank you for visiting us today!          |
      | Event           | Join us for this special event!           |
    And I click "Save Branding"
    Then the branding should be applied throughout the system
    And custom messages should be used in communications

  @p2
  Scenario: Organization data import from existing system
    Given I have data from a previous church management system
    And I want to migrate to ChMS
    When I navigate to "Data Import"
    And I upload data files:
      | Data Type       | File Format | Records |
      | Members         | CSV         | 500     |
      | Services        | CSV         | 50      |
      | Attendance      | CSV         | 2000    |
      | Departments     | CSV         | 10      |
    And I map fields to ChMS structure
    And I click "Import Data"
    Then all data should be imported successfully
    And I should see "Data import completed: 500 members, 50 services, 2000 attendance records"
    And data integrity should be maintained

  @p2
  Scenario: Organization backup and export
    Given my organization has been operating for some time
    And I want to backup organization data
    When I navigate to "Data Management"
    And I select "Full Organization Backup"
    And I choose backup options:
      | Option              | Setting  |
      | Include Members     | Yes      |
      | Include Attendance  | Yes      |
      | Include Services    | Yes      |
      | Include Settings    | Yes      |
      | Format             | JSON     |
    And I click "Create Backup"
    Then a complete backup should be generated
    And I should be able to download the backup file
    And the backup should include all selected data

  @p1
  Scenario: Organization settings validation
    Given I am updating organization settings
    When I enter invalid information:
      | Field    | Invalid Value     |
      | Email    | invalid-email     |
      | Phone    | abc123           |
      | Website  | not-a-url        |
    And I attempt to save
    Then I should see validation errors
    And the errors should specify the correct format for each field
    And the settings should not be saved until valid data is provided
