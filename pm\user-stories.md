# User Stories - ChMS

This document outlines user stories for the ChMS project, describing features from the perspective of end-users.

## Core Functionality

**1. Member Management**

- **As a Church Administrator, I want to add a new member profile** including their name, contact information, and join date, **so that** I can maintain an accurate church directory.
- **As a Church Administrator, I want to view and update member details** (e.g., address, phone number), **so that** the directory information remains current.
- **As a Church Administrator, I want to group members into family units,** **so that** I can understand household connections and manage related communications.
- **As a Church Administrator, I want to access member profiles even when my internet connection is unstable,** **so that** I can perform essential tasks reliably.

**2. Attendance Tracking**

- **As a Service Leader, I want to quickly record attendance for a church service,** possibly using a QR code scanner or manual check-in, **so that** we have accurate attendance records.
- **As a Church Administrator, I want to view attendance reports for past services or events,** **so that** I can track engagement and trends.

**3. Authentication & Access**

- **As a Church Administrator, I want to securely log in to the system** using my credentials, **so that** only authorized personnel can access church data.
- **As a User, I want the system to remember my login session** for a reasonable period, **so that** I don't have to log in repeatedly.

## Potential Future Features

- **As an Event Coordinator, I want to create and manage church events,** including details like date, time, location, and description, **so that** members can be informed.
- **As a Church Administrator, I want to record information about first-time visitors,** **so that** we can follow up and welcome them properly.
- **As a Group Leader, I want to manage members within my specific ministry group,** **so that** I can track participation and communicate effectively.

_(This list will be expanded as features are further defined.)_
