import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AccountLockoutService } from '@/services/auth/account-lockout';
import { prismaMock } from '../../__mocks__/prisma';
import { SECURITY_CONSTANTS } from '@/config/security';

vi.mock('@/lib/prisma', () => ({
  prisma: prismaMock,
}));

vi.mock('@/lib/logger', () => ({
  logger: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
  },
}));

describe('AccountLockoutService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('checkLockoutStatus', () => {
    it('should return unlocked status for new user', async () => {
      prismaMock.user.findUnique.mockResolvedValue(null);

      const status = await AccountLockoutService.checkLockoutStatus('<EMAIL>');

      expect(status.isLocked).toBe(false);
      expect(status.remainingAttempts).toBe(SECURITY_CONSTANTS.ACCOUNT_LOCKOUT.MAX_FAILED_ATTEMPTS);
    });

    it('should return locked status for locked account', async () => {
      const lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now
      
      prismaMock.user.findUnique.mockResolvedValue({
        failedLoginAttempts: 5,
        lastFailedLoginAt: new Date(),
        lockedUntil,
      });

      const status = await AccountLockoutService.checkLockoutStatus('<EMAIL>');

      expect(status.isLocked).toBe(true);
      expect(status.remainingAttempts).toBe(0);
      expect(status.lockoutExpiresAt).toEqual(lockedUntil);
    });

    it('should return unlocked status for expired lockout', async () => {
      const expiredLockout = new Date(Date.now() - 60 * 1000); // 1 minute ago
      
      prismaMock.user.findUnique.mockResolvedValue({
        failedLoginAttempts: 5,
        lastFailedLoginAt: new Date(),
        lockedUntil: expiredLockout,
      });

      const status = await AccountLockoutService.checkLockoutStatus('<EMAIL>');

      expect(status.isLocked).toBe(false);
      expect(status.remainingAttempts).toBe(0); // Still shows 0 until reset
    });

    it('should calculate progressive delay', async () => {
      prismaMock.user.findUnique.mockResolvedValue({
        failedLoginAttempts: 2,
        lastFailedLoginAt: new Date(),
        lockedUntil: null,
      });

      const status = await AccountLockoutService.checkLockoutStatus('<EMAIL>');

      expect(status.isLocked).toBe(false);
      expect(status.nextAttemptDelay).toBeGreaterThan(0);
      expect(status.remainingAttempts).toBe(3); // 5 - 2 = 3
    });
  });

  describe('recordFailedAttempt', () => {
    it('should increment failed attempts for existing user', async () => {
      const userId = 'user123';
      prismaMock.user.findUnique.mockResolvedValue({
        id: userId,
        failedLoginAttempts: 2,
        lockedUntil: null,
      });

      prismaMock.user.update.mockResolvedValue({
        id: userId,
        failedLoginAttempts: 3,
      });

      const status = await AccountLockoutService.recordFailedAttempt('<EMAIL>', '192.168.1.1');

      expect(prismaMock.user.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: expect.objectContaining({
          failedLoginAttempts: 3,
          lastFailedLoginAt: expect.any(Date),
        }),
      });

      expect(status.isLocked).toBe(false);
      expect(status.remainingAttempts).toBe(2); // 5 - 3 = 2
    });

    it('should lock account when max attempts reached', async () => {
      const userId = 'user123';
      prismaMock.user.findUnique.mockResolvedValue({
        id: userId,
        failedLoginAttempts: 4, // One less than max
        lockedUntil: null,
      });

      prismaMock.user.update.mockResolvedValue({
        id: userId,
        failedLoginAttempts: 5,
        lockedUntil: new Date(),
      });

      const status = await AccountLockoutService.recordFailedAttempt('<EMAIL>', '192.168.1.1');

      expect(prismaMock.user.update).toHaveBeenCalledWith({
        where: { id: userId },
        data: expect.objectContaining({
          failedLoginAttempts: 5,
          lockedUntil: expect.any(Date),
        }),
      });

      expect(status.isLocked).toBe(true);
      expect(status.remainingAttempts).toBe(0);
    });

    it('should handle non-existent user without revealing existence', async () => {
      prismaMock.user.findUnique.mockResolvedValue(null);

      const status = await AccountLockoutService.recordFailedAttempt('<EMAIL>');

      expect(status.isLocked).toBe(false);
      expect(status.remainingAttempts).toBe(4); // One less than max, as if attempt was recorded
      expect(prismaMock.user.update).not.toHaveBeenCalled();
    });
  });

  describe('resetFailedAttempts', () => {
    it('should reset failed attempts and unlock account', async () => {
      prismaMock.user.updateMany.mockResolvedValue({ count: 1 });

      await AccountLockoutService.resetFailedAttempts('<EMAIL>');

      expect(prismaMock.user.updateMany).toHaveBeenCalledWith({
        where: { 
          email: '<EMAIL>',
          OR: [
            { failedLoginAttempts: { gt: 0 } },
            { lockedUntil: { not: null } },
          ],
        },
        data: {
          failedLoginAttempts: 0,
          lastFailedLoginAt: null,
          lockedUntil: null,
        },
      });
    });
  });

  describe('unlockAccount', () => {
    it('should manually unlock account', async () => {
      prismaMock.user.updateMany.mockResolvedValue({ count: 1 });

      await AccountLockoutService.unlockAccount('<EMAIL>', '<EMAIL>');

      expect(prismaMock.user.updateMany).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
        data: {
          failedLoginAttempts: 0,
          lastFailedLoginAt: null,
          lockedUntil: null,
        },
      });
    });
  });

  describe('getLockoutStats', () => {
    it('should return lockout statistics', async () => {
      prismaMock.user.count
        .mockResolvedValueOnce(3) // currently locked accounts
        .mockResolvedValueOnce(7); // recent failed attempts

      const stats = await AccountLockoutService.getLockoutStats('day');

      expect(stats.currentlyLocked).toBe(3);
      expect(stats.recentFailedAttempts).toBe(7);
      expect(stats.timeframe).toBe('day');
    });

    it('should handle errors gracefully', async () => {
      prismaMock.user.count.mockRejectedValue(new Error('Database error'));

      const stats = await AccountLockoutService.getLockoutStats('hour');

      expect(stats.currentlyLocked).toBe(0);
      expect(stats.recentFailedAttempts).toBe(0);
      expect(stats.timeframe).toBe('hour');
    });
  });
});
