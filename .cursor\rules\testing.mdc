---
description: Testing rules and guidelines
globs:
alwaysApply: true
---

# Testing Rules

## Core Requirements

1. Use @pm/test-checklist.md as working document (focus P0 first)
2. Reference @pm/testing-strategy.md and @docs/standards/testing-standards.md
3. Track in @pm/test-checklist.md
4. Follow @docs/tutorials/testing-strategy.md for implementation guidance

## Database Handling
- Database can be reset freely during tests (no production data)
- Use test transactions where possible
- Clean up test data after each test

## Test Implementation Template
```
Component: [NAME]
Priority: [P0/P1/P2]
Goal: [GOAL]
Success: [CRITERIA]
```

## Standards
- Testing: Vitest + RTL + MSW + Cypress
- UI: react-icons only (no @chakra-ui/icons)
- Styling: Chakra UI v3 only (no Tailwind)

## Coverage
- P0: 90%+
- P1: 80%+
- P2: 60%+

## Example Goals

### P0 (Authentication)
- Component: `LoginForm`
- Goal: Secure auth with error handling
- Success: Login flow, invalid handling, session verified

### P0 (Integration)
- Component: `AttendanceRecorder`
- Goal: Complete attendance flow
- Success: QR scan → validate → DB update

### P1 (UI)
- Component: `DataTable`
- Goal: Table operations working
- Success: Sort/filter/paginate verified