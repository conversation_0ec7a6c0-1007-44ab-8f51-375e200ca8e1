import { Session } from 'next-auth';

export const mockSession: Session = {
  user: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'VIEWER',
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
};

export const mockAdminSession: Session = {
  user: {
    id: 'admin-user-id',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'ADMIN',
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
};

export const mockSuperAdminSession: Session = {
  user: {
    id: 'super-admin-id',
    email: '<EMAIL>',
    name: 'Super Admin',
    role: 'SUPER_ADMIN',
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
};
