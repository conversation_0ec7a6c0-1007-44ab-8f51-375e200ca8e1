@mvp @p1 @visitor-management
Feature: Visitor Registration System
  As a Church Greeter
  I want to register first-time visitors and collect their information
  So that we can follow up and welcome them into our church community

  Background:
    Given I am logged in as a Church Greeter or Staff Member
    And the visitor registration system is available
    And I have permissions to register visitors

  @p1 @smoke
  Scenario: Register a first-time visitor
    Given I am at the visitor registration station
    When I click "Register New Visitor"
    And I enter visitor information:
      | Field          | Value                    |
      | First Name     | Jennifer                 |
      | Last Name      | Williams                 |
      | Email          | <EMAIL>       |
      | Phone          | +1234567890              |
      | Address        | 789 Pine Street          |
      | How heard      | Friend invitation        |
      | Interests      | Bible study, community   |
    And I click "Register Visitor"
    Then the visitor should be registered successfully
    And I should see "<PERSON> registered as visitor"
    And a visitor profile should be created
    And the visitor should be flagged for follow-up

  @p1
  Scenario: Quick visitor check-in for returning visitor
    Given visitor "<PERSON>" was registered previously
    And she is visiting again today
    When I search for "<PERSON>" in the visitor system
    And I select her from the search results
    And I click "Check In Returning Visitor"
    Then her attendance should be recorded for today
    And I should see "Welcome back, <PERSON>!"
    And her visit count should be incremented
    And she should be considered for membership follow-up

  @p1
  Scenario: Visitor registration with family members
    Given I am registering a visiting family
    When I click "Register Visitor Family"
    And I enter the primary contact information:
      | Field          | Value                    |
      | Family Name    | The Johnson Family       |
      | Primary Contact| <PERSON>             |
      | Email          | <EMAIL>           |
      | Phone          | +1234567890              |
    And I add family members:
      | Name           | Age | Relationship |
      | Mark Johnson   | 40  | Father       |
      | Lisa Johnson   | 38  | Mother       |
      | Emma Johnson   | 12  | Daughter     |
      | Jake Johnson   | 10  | Son          |
    And I click "Register Family"
    Then all family members should be registered as visitors
    And a family unit should be created
    And follow-up should be assigned to the primary contact

  @p1
  Scenario: Visitor information validation
    Given I am registering a new visitor
    When I enter invalid information:
      | Field     | Invalid Value    |
      | Email     | invalid-email    |
      | Phone     | abc123          |
    And I attempt to register
    Then I should see validation errors
    And the error should specify "Please enter a valid email address"
    And the error should specify "Please enter a valid phone number"
    And the registration should not be completed

  @p1
  Scenario: Visitor welcome packet generation
    Given I have registered visitor "Sarah Davis"
    When the registration is completed
    Then a welcome packet should be automatically prepared
    And the packet should include:
      | Item                    | Description                           |
      | Welcome Letter          | Personalized greeting from pastor     |
      | Church Information      | Service times, programs, contact info |
      | Connection Card         | For ongoing communication preferences |
      | Ministry Opportunities  | Ways to get involved                  |
      | Next Steps Guide        | Path to membership and engagement     |
    And I should be able to print the welcome packet

  @p0
  Scenario: Duplicate visitor prevention
    Given visitor "Jennifer Williams" with email "<EMAIL>" already exists
    When I attempt to register a new visitor with the same email
    Then I should see "A visitor with this email already exists"
    And I should be given options to:
      | Option                    | Action                                |
      | Check in existing visitor | Record attendance for existing record |
      | Update visitor info       | Modify existing visitor information   |
      | Create new record         | Override with different email         |

  @p1
  Scenario: Visitor privacy preferences
    Given I am registering a new visitor
    When I reach the privacy preferences section
    Then I should be able to set:
      | Preference              | Options                    |
      | Email Communications    | Yes/No                     |
      | Phone Contact          | Yes/No                     |
      | Mail Communications    | Yes/No                     |
      | Photo Permission       | Yes/No                     |
      | Directory Inclusion    | Yes/No                     |
    And these preferences should be respected in all communications
    And the visitor should be able to update preferences later

  @p2
  Scenario: Visitor registration analytics
    Given multiple visitors have been registered over time
    When I view the visitor registration dashboard
    Then I should see analytics including:
      | Metric                  | Description                        |
      | New Visitors This Week  | Count of first-time registrations  |
      | Returning Visitors      | Previously registered visitors     |
      | Conversion Rate         | Visitors who became members        |
      | Follow-up Completion    | Percentage of completed follow-ups |
      | Source Tracking         | How visitors heard about church    |
    And I should be able to export visitor reports

  @p2
  Scenario: Visitor registration via mobile device
    Given I am using a mobile device for visitor registration
    When I access the visitor registration form
    Then the form should be optimized for mobile input
    And I should be able to use device features:
      | Feature           | Usage                              |
      | Camera           | Take visitor photo if permitted    |
      | Location         | Auto-fill address information      |
      | Contacts         | Quick contact information entry    |
    And the registration should work smoothly on mobile

  @p2
  Scenario: Bulk visitor import from events
    Given we hosted a community event with visitor sign-ups
    And I have a CSV file with visitor information from the event
    When I navigate to "Import Event Visitors"
    And I upload the CSV file
    And I specify the event details:
      | Field       | Value                    |
      | Event Name  | Community Outreach Fair  |
      | Event Date  | October 15, 2023         |
      | Event Type  | Outreach                 |
    And I click "Import Visitors"
    Then all visitors should be imported successfully
    And each visitor should be tagged with the event information
    And appropriate follow-up workflows should be triggered
