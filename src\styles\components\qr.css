/* QR Scanner Styles */
.qr-scanner-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.qr-scanner-viewport {
  position: relative;
  width: 100%;
  max-width: 300px;
  height: 300px;
  border: 2px solid;
  border-color: var(--primary-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.qr-scanner-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.qr-scanner-error {
  height: 100%;
  padding: var(--spacing-md);
  background: var(--chakra-colors-gray-100);
}

/* QR Code Generator Styles */
.qr-generator-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: var(--spacing-md);
}

.qr-code-display {
  width: 100%;
  max-width: 300px;
  height: 300px;
  margin: 0 auto;
  padding: var(--spacing-sm);
  background: white;
  border-radius: var(--radius-md);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.qr-code-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
