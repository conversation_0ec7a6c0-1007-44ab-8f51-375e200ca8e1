# File Relationship & Module Structure (MVP)

This document describes the main files and modules in the ChMS codebase, their relationships, and how they support the MVP features as defined in the latest PRD.

---

## 🌍 West Africa Context Mapping

Modules and files labeled `africa-priority` or `low-bandwidth` are designed to address offline-first, device compatibility, WhatsApp/USSD fallback, Lite mode, local payment, and multi-language support.

| Module/Feature                | Context Priority         | Label(s)           |
|-------------------------------|-------------------------|--------------------|
| attendance/                   | Offline, WhatsApp/USSD  | africa-priority    |
| hooks/useDataSync.ts          | Offline queue/sync      | africa-priority    |
| components/attendance/        | Lite mode, mobile-first | low-bandwidth      |
| services/payments/ (future)   | Local payment           | africa-priority    |
| components/language/ (future) | Multi-language          | africa-priority    |
| services/ai/                  | Server-side AI, language simplification | africa-priority |

---

## 1. Core Structure

```
src/
├── components/         # UI components (attendance, auth, onboarding, etc.)
├── hooks/              # Custom React hooks
├── pages/              # Next.js pages (routing, API endpoints)
├── services/           # Business logic, API integrations
├── store/              # State management (attendance, members, org)
├── styles/             # Global and component styles
├── types/              # TypeScript types
├── utils/              # Utility functions
```

---

## 2. Key MVP Modules & Relationships

### Authentication
- `components/auth/LoginForm.tsx` — Login UI
- `services/auth/` — Auth logic (NextAuth.js, session management)
- `store/` — Session state
- `pages/api/auth/` — API endpoints for authentication

### Attendance *(africa-priority, low-bandwidth)*
- `components/attendance/` — QRScanner, CheckInForm, AttendanceList, WhatsApp/USSD fallback, Lite mode
- `services/attendance/` — Attendance logic (check-in, sync, offline queue)
- `store/attendanceStore.ts` — Attendance state, offline queue
- `hooks/useDataSync.ts` — Data sync logic
- `pages/api/attendance/` — API endpoints for attendance

### Member & Organization Management
- `components/users/ImportUsers.tsx` — Member import UI
- `components/organisation/` — Org management UI
- `services/organization/` — Org logic
- `store/memberStore.ts` — Member state
- `store/config.ts` — Org config state
- `pages/api/organizations/` — Org API endpoints

### AI-Native Onboarding & Automation (MVP)
- `services/ai/` — Flowise agent orchestration, n8n automation logic, server-side AI for low-end devices *(africa-priority)*
- `components/onboarding/` — Conversational onboarding UI
- `pages/api/ai/` — API endpoints for AI agent flows
- `components/language/` (future) — Language simplification/translation *(africa-priority)*

### Offline-First Support *(africa-priority, low-bandwidth)*
- `hooks/useDataSync.ts` — Data sync logic
- `store/attendanceStore.ts` — Local queue for attendance
- `services/attendance/` — Sync and conflict resolution
- `components/attendance/LiteMode.tsx` (future) — Lite UI for low-end devices

### Payments *(africa-priority, future)*
- `services/payments/` — Flutterwave, Paystack, Momo integration

### Multi-language Support *(africa-priority, future)*
- `components/language/` — Multi-language UI, Pidgin/French support

---

## 3. Relationships Diagram (MVP)

```mermaid
graph TD;
  LoginForm --> AuthService;
  AuthService --> SessionStore;
  CheckInForm --> AttendanceService;
  AttendanceService --> AttendanceStore;
  AttendanceStore --> DataSync;
  ImportUsers --> MemberStore;
  MemberStore --> OrganizationService;
  AIOnboarding --> AIService;
  AIService --> n8n;
  AIService --> Flowise;
  AttendanceService --> WhatsAppUSSD;
  AttendanceService --> LiteMode;
  AIService --> LanguageModule;
  PaymentsService --> PaymentProviders;
```

---

## 4. Advanced/Extra Features *(future)*
- Analytics dashboards
- Visitor management
- Advanced reporting
- Profile image upload
- Filtering system
- Communication system (email, SMS, WhatsApp)
- Payments (Flutterwave, Paystack, Momo)
- Multi-language UI

These modules/files are present in the codebase but are marked as 'future' and not part of the MVP. See the test checklist and tracker for details.

---

## 5. How to Update
- Update this file as new modules are added or removed.
- Mark any new features as 'future' if not in the current MVP.
- Keep diagrams and lists in sync with the codebase, PRD, and West Africa context. 