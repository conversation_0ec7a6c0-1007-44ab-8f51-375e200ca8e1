"use client";

import { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  VStack,
  Select,
  FormControl,
  FormLabel,
  useToast,
  Spinner,
  Text,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import CheckInForm from '@/components/attendance/CheckInForm'; // Adjust path if needed

interface Member {
  id: string;
  name: string; // Assuming 'name' for display, Prisma schema has firstName, lastName
  firstName: string;
  lastName: string;
}

interface Service {
  id: string;
  name: string;
  date: string; // Assuming date is part of service details for display
}

interface CheckInData {
  memberId?: string;
  memberIds?: string[];
  type: "INDIVIDUAL" | "FAMILY";
  serviceId: string;
}

export default function ManualCheckInPage() {
  const [members, setMembers] = useState<Member[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [selectedServiceId, setSelectedServiceId] = useState<string>('');
  
  const [isLoadingMembers, setIsLoadingMembers] = useState(true);
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();

  // Fetch Members
  useEffect(() => {
    const fetchMembers = async () => {
      setIsLoadingMembers(true);
      setError(null);
      try {
        // Assuming an API endpoint to get members.
        // Using /api/members which was read earlier.
        const response = await fetch('/api/members'); 
        if (!response.ok) {
          throw new Error('Failed to fetch members');
        }
        const data = await response.json();
        // Adapt if member structure is different, e.g., combining firstName and lastName
        setMembers(data.map((m: Member) => ({ ...m, name: `${m.firstName} ${m.lastName}`.trim() })));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Could not load members.');
        console.error(err);
      } finally {
        setIsLoadingMembers(false);
      }
    };
    fetchMembers();
  }, []);

  // Fetch Services
  useEffect(() => {
    const fetchServices = async () => {
      setIsLoadingServices(true);
      setError(null);
      try {
        const response = await fetch('/api/services'); // Endpoint confirmed earlier
        if (!response.ok) {
          throw new Error('Failed to fetch services');
        }
        const data = await response.json();
        setServices(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Could not load services.');
        console.error(err);
      } finally {
        setIsLoadingServices(false);
      }
    };
    fetchServices();
  }, []);

  const handleCheckInSubmit = async (data: CheckInData) => {
    if (!data.serviceId) {
      toast({
        title: 'Service not selected',
        description: 'Please select a service before checking in.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    try {
      const response = await fetch('/api/attendance/check-in', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();
      if (!response.ok) {
        throw new Error(responseData.error || 'Check-in failed');
      }

      toast({
        title: 'Check-in Successful',
        description: data.type === 'FAMILY' 
          ? `${data.memberIds?.length || 0} members checked in.` 
          : 'Member checked in.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      // Optionally, clear form or update UI
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred during check-in.';
      setError(errorMessage);
      toast({
        title: 'Check-in Failed',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoadingMembers || isLoadingServices) {
    return (
      <Container centerContent py={10}>
        <Spinner size="xl" />
        <Text mt={4}>Loading data...</Text>
      </Container>
    );
  }

  return (
    <Container py={8}>
      <VStack spacing={8} align="stretch">
        <Heading as="h1" size="xl" textAlign="center">
          Manual Attendance Check-In
        </Heading>

        {error && (
          <Alert status="error">
            <AlertIcon />
            {error}
          </Alert>
        )}

        <Box p={6} borderWidth={1} borderRadius="md" boxShadow="sm">
          <VStack spacing={6}>
            <FormControl isRequired>
              <FormLabel htmlFor="service-select">Select Service</FormLabel>
              <Select
                id="service-select"
                placeholder="Choose a service"
                value={selectedServiceId}
                onChange={(e) => setSelectedServiceId(e.target.value)}
                isDisabled={services.length === 0}
              >
                {services.map((service) => (
                  <option key={service.id} value={service.id}>
                    {service.name} - {new Date(service.date).toLocaleDateString()}
                  </option>
                ))}
              </Select>
              {services.length === 0 && <Text fontSize="sm" color="gray.500" mt={1}>No services available.</Text>}
            </FormControl>

            {members.length === 0 && !isLoadingMembers && (
                <Text color="gray.500">No members found. Please add members before attempting check-in.</Text>
            )}
            
            {members.length > 0 && (
              <CheckInForm
                members={members}
                serviceId={selectedServiceId}
                onSubmit={handleCheckInSubmit}
              />
            )}
            
            {isSubmitting && (
                <VStack justifyContent="center" alignItems="center" mt={4}>
                    <Spinner />
                    <Text>Processing check-in...</Text>
                </VStack>
            )}
          </VStack>
        </Box>
      </VStack>
    </Container>
  );
}
