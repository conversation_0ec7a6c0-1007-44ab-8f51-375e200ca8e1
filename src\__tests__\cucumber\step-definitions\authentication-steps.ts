import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { CustomWorld } from '../support/world';

// Login scenarios
Given('I am on the login page', async function(this: CustomWorld) {
  await this.navigateTo('/login');
});

When('I enter valid credentials', async function(this: CustomWorld) {
  await this.fillForm({
    email: '<EMAIL>',
    password: 'admin123'
  });
});

When('I enter invalid credentials', async function(this: CustomWorld) {
  await this.fillForm({
    email: '<EMAIL>',
    password: 'wrongpassword'
  });
});

When('I enter credentials:', async function(this: CustomWorld, dataTable) {
  const credentials = dataTable.rowsHash();
  await this.fillForm(credentials);
});

When('I click the login button', async function(this: CustomWorld) {
  if (this.page) {
    await this.page.click('[data-testid="submit"], button[type="submit"]');
  }
});

Then('I should be redirected to the dashboard', async function(this: CustomWorld) {
  if (this.page) {
    await expect(this.page).toHaveURL(/.*\/dashboard/);
    await expect(this.page.locator('h1')).toContainText('Dashboard');
  }
});

Then('I should see an invalid credentials error', async function(this: CustomWorld) {
  await this.page?.waitForSelector('[data-testid="error-message"]');
  const errorMessage = await this.page?.textContent('[data-testid="error-message"]');
  expect(errorMessage).toContain('Invalid email or password');
});

// Account lockout scenarios
Given('I have made {int} failed login attempts', async function(this: CustomWorld, attempts: number) {
  for (let i = 0; i < attempts; i++) {
    await this.fillForm({
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    await this.page?.click('[data-testid="submit"]');
    await this.page?.waitForSelector('[data-testid="error-message"]');
  }
});

When('I make another failed login attempt', async function(this: CustomWorld) {
  await this.fillForm({
    email: '<EMAIL>',
    password: 'wrongpassword'
  });
  await this.page?.click('[data-testid="submit"]');
});

Then('I should see an account lockout message', async function(this: CustomWorld) {
  await this.page?.waitForSelector('[data-testid="lockout-message"]');
  const lockoutMessage = await this.page?.textContent('[data-testid="lockout-message"]');
  expect(lockoutMessage).toContain('Account temporarily locked');
});

Then('I should see a lockout duration of {string}', async function(this: CustomWorld, duration: string) {
  const lockoutMessage = await this.page?.textContent('[data-testid="lockout-message"]');
  expect(lockoutMessage).toContain(duration);
});

// Progressive lockout scenarios
Given('my account has been locked {int} times before', async function(this: CustomWorld, lockoutCount: number) {
  this.testData.previousLockouts = lockoutCount;
  // Mock the user's lockout history in the database
  this.db.user.findUnique.mockResolvedValue({
    id: '1',
    email: '<EMAIL>',
    lockoutCount: lockoutCount,
    lockedUntil: null
  });
});

Then('the lockout duration should be {string}', async function(this: CustomWorld, expectedDuration: string) {
  const lockoutMessage = await this.page?.textContent('[data-testid="lockout-message"]');
  expect(lockoutMessage).toContain(expectedDuration);
});

// Session management scenarios
Given('I am logged in with a valid session', async function(this: CustomWorld) {
  await this.loginAs('admin');
  // Verify session is active
  if (this.page) {
    await expect(this.page.locator('[data-testid="user-menu"]')).toBeVisible();
  }
});

When('my session expires', async function(this: CustomWorld) {
  // Mock session expiration
  if (this.page) {
    await this.page.evaluate(() => {
      localStorage.removeItem('auth-token');
      sessionStorage.clear();
    });
    await this.page.reload();
  }
});

Then('I should be redirected to the login page', async function(this: CustomWorld) {
  if (this.page) {
    await expect(this.page).toHaveURL(/.*\/login/);
  }
});

Then('I should see a session expired message', async function(this: CustomWorld) {
  const message = await this.page?.textContent('[data-testid="session-message"]');
  expect(message).toContain('session has expired');
});

// Password reset scenarios
Given('I am on the password reset page', async function(this: CustomWorld) {
  await this.navigateTo('/reset-password');
});

When('I enter my email address {string}', async function(this: CustomWorld, email: string) {
  await this.page?.fill('[data-testid="email"]', email);
});

When('I click the reset password button', async function(this: CustomWorld) {
  await this.page?.click('[data-testid="reset-button"]');
});

Then('I should see a password reset confirmation', async function(this: CustomWorld) {
  await this.page?.waitForSelector('[data-testid="reset-confirmation"]');
  const confirmation = await this.page?.textContent('[data-testid="reset-confirmation"]');
  expect(confirmation).toContain('password reset link has been sent');
});

// Two-factor authentication scenarios
Given('two-factor authentication is enabled for my account', async function(this: CustomWorld) {
  this.testData.twoFactorEnabled = true;
  // Mock user with 2FA enabled
  this.db.user.findUnique.mockResolvedValue({
    id: '1',
    email: '<EMAIL>',
    twoFactorEnabled: true,
    twoFactorSecret: 'mock-secret'
  });
});

When('I enter the correct 2FA code', async function(this: CustomWorld) {
  await this.page?.fill('[data-testid="2fa-code"]', '123456');
});

When('I enter an incorrect 2FA code', async function(this: CustomWorld) {
  await this.page?.fill('[data-testid="2fa-code"]', '000000');
});

Then('I should see the 2FA verification page', async function(this: CustomWorld) {
  if (this.page) {
    await expect(this.page.locator('[data-testid="2fa-form"]')).toBeVisible();
    await expect(this.page.locator('h1')).toContainText('Two-Factor Authentication');
  }
});

Then('I should see a 2FA error message', async function(this: CustomWorld) {
  const errorMessage = await this.page?.textContent('[data-testid="2fa-error"]');
  expect(errorMessage).toContain('Invalid verification code');
});

// Role-based access scenarios
Given('I am logged in as a {string}', async function(this: CustomWorld, role: string) {
  await this.loginAs(role as 'admin' | 'pastor' | 'member' | 'visitor');
});

When('I try to access the admin panel', async function(this: CustomWorld) {
  await this.navigateTo('/admin');
});

Then('I should see the admin dashboard', async function(this: CustomWorld) {
  if (this.page) {
    await expect(this.page.locator('[data-testid="admin-dashboard"]')).toBeVisible();
  }
});

Then('I should see an access denied message', async function(this: CustomWorld) {
  const message = await this.page?.textContent('[data-testid="access-denied"]');
  expect(message).toContain('Access denied');
});

// Logout scenarios
When('I click the logout button', async function(this: CustomWorld) {
  await this.page?.click('[data-testid="logout-button"]');
});

Then('I should be logged out successfully', async function(this: CustomWorld) {
  if (this.page) {
    await expect(this.page).toHaveURL(/.*\/login/);
    await expect(this.page.locator('[data-testid="user-menu"]')).not.toBeVisible();
  }
});

// Security scenarios
Given('I have enabled security notifications', async function(this: CustomWorld) {
  this.testData.securityNotifications = true;
});

When('I log in from a new device', async function(this: CustomWorld) {
  // Mock new device login
  this.testData.newDevice = true;
  await this.fillForm({
    email: '<EMAIL>',
    password: 'admin123'
  });
  await this.page?.click('[data-testid="submit"]');
});

Then('I should receive a security notification', async function(this: CustomWorld) {
  // In a real implementation, this would check for email/SMS notifications
  // For testing, we'll check for a notification banner
  await this.page?.waitForSelector('[data-testid="security-notification"]');
  const notification = await this.page?.textContent('[data-testid="security-notification"]');
  expect(notification).toContain('New device login detected');
});
